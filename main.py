"""
针策略回测主程序

整合所有模块，实现完整的回测和优化流程
"""
import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import argparse
import json

# 添加src目录到路径
sys.path.append('src')

from data_downloader import BinanceDataDownloader
from signal_generator import SignalGenerator
from backtest_engine import BacktestEngine
from parameter_optimizer import ParameterOptimizer, create_objective_function
from statistical_analysis import StatisticalAnalyzer
from visualization import BacktestVisualizer


class NeedleStrategyBacktest:
    """针策略回测主类"""
    
    def __init__(self, config: dict = None):
        """
        初始化回测系统
        
        Args:
            config: 配置字典
        """
        self.config = config or self.get_default_config()
        self.data_downloader = BinanceDataDownloader(self.config['data_dir'])
        self.analyzer = StatisticalAnalyzer()
        
        # 创建输出目录
        self.output_dir = self.create_output_directory()
        self.visualizer = BacktestVisualizer(self.output_dir)
        
        self.price_data = None
        self.analysis_results = None
        
    def get_default_config(self) -> dict:
        """获取默认配置"""
        return {
            'symbol': 'BTCUSDT',
            'interval': '1h',
            'start_date': '2024-01-01',
            'end_date': '2024-06-30',
            'data_dir': 'data',
            'initial_capital': 100000,
            'commission_rate': 0.001,
            'slippage_rate': 0.0005,
            'risk_per_trade': 0.02,
            'needle_params': {
                'atr_period': 14,
                'min_shadow_atr_ratio': 1.5,
                'max_body_ratio': 0.3,
                'max_opposite_shadow_ratio': 0.2
            },
            'signal_params': {
                'ma_period': 20,
                'ma_type': 'ema',
                'min_needle_strength': 1.0,
                'risk_reward_ratio': 2.0
            }
        }
    
    def create_output_directory(self) -> str:
        """创建输出目录"""
        # 使用参数组合命名目录
        params = self.config
        dir_name = f"{params['symbol']}_{params['interval']}_{params['start_date']}_{params['end_date']}"
        dir_name += f"_atr{params['needle_params']['atr_period']}"
        dir_name += f"_ma{params['signal_params']['ma_period']}"
        dir_name += f"_rr{params['signal_params']['risk_reward_ratio']}"
        
        output_dir = os.path.join('output', dir_name)
        os.makedirs(output_dir, exist_ok=True)
        
        return output_dir
    
    def download_data(self) -> pd.DataFrame:
        """下载数据"""
        print("=" * 60)
        print("步骤1: 下载历史数据")
        print("=" * 60)
        
        self.price_data = self.data_downloader.download_data(
            symbol=self.config['symbol'],
            interval=self.config['interval'],
            start_date=self.config['start_date'],
            end_date=self.config['end_date']
        )
        
        if self.price_data is None:
            raise ValueError("数据下载失败")
        
        # 保存数据
        data_path = os.path.join(self.output_dir, 'price_data.csv')
        self.price_data.to_csv(data_path)
        print(f"价格数据已保存到: {data_path}")
        
        return self.price_data
    
    def analyze_needle_distribution(self) -> dict:
        """分析针指标分布"""
        print("\n" + "=" * 60)
        print("步骤2: 分析针指标分布")
        print("=" * 60)
        
        if self.price_data is None:
            raise ValueError("请先下载数据")
        
        # 分析针分布
        self.analysis_results = self.analyzer.analyze_needle_distribution(
            self.price_data, atr_periods=[10, 14, 21, 28]
        )
        
        # 生成参数建议
        suggestions = self.analyzer.suggest_parameter_ranges(self.analysis_results)
        
        # 保存分析结果
        analysis_path = os.path.join(self.output_dir, 'needle_analysis.json')
        with open(analysis_path, 'w', encoding='utf-8') as f:
            # 转换numpy类型为Python原生类型以便JSON序列化
            serializable_results = self._make_json_serializable(self.analysis_results)
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        
        # 生成分析报告
        report = self.analyzer.generate_analysis_report(self.analysis_results)
        report_path = os.path.join(self.output_dir, 'analysis_report.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"分析结果已保存到: {analysis_path}")
        print(f"分析报告已保存到: {report_path}")
        
        # 绘制分布图
        plot_path = os.path.join(self.output_dir, 'needle_distribution.png')
        self.analyzer.plot_distributions(self.analysis_results, plot_path)
        
        return suggestions
    
    def _make_json_serializable(self, obj):
        """将对象转换为JSON可序列化格式"""
        if isinstance(obj, dict):
            return {k: self._make_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(v) for v in obj]
        elif isinstance(obj, pd.DataFrame):
            return "DataFrame_object"  # 不序列化DataFrame
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj
    
    def run_single_backtest(self, parameters: dict = None) -> dict:
        """运行单次回测"""
        print("\n" + "=" * 60)
        print("步骤3: 运行回测")
        print("=" * 60)
        
        if self.price_data is None:
            raise ValueError("请先下载数据")
        
        # 使用提供的参数或默认参数
        params = parameters or self.config
        
        # 创建信号生成器
        signal_generator = SignalGenerator(
            needle_params=params['needle_params'],
            ma_period=params['signal_params']['ma_period'],
            ma_type=params['signal_params']['ma_type'],
            min_needle_strength=params['signal_params']['min_needle_strength'],
            risk_reward_ratio=params['signal_params']['risk_reward_ratio']
        )
        
        # 生成交易信号
        print("生成交易信号...")
        signals_df = signal_generator.generate_complete_signals(self.price_data)
        signals_df = signal_generator.filter_signals_by_time(signals_df, 4)
        
        # 保存信号数据
        signals_path = os.path.join(self.output_dir, 'signals.csv')
        signals_df.to_csv(signals_path)
        
        # 创建回测引擎
        backtest_engine = BacktestEngine(
            initial_capital=params['initial_capital'],
            commission_rate=params['commission_rate'],
            slippage_rate=params['slippage_rate'],
            risk_per_trade=params['risk_per_trade']
        )
        
        # 运行回测
        print("运行回测...")
        metrics = backtest_engine.run_backtest(self.price_data, signals_df)
        
        # 获取详细结果
        trades_df = backtest_engine.get_trade_analysis()
        equity_df = backtest_engine.get_equity_curve_df()
        
        # 保存结果
        trades_path = os.path.join(self.output_dir, 'trades.csv')
        equity_path = os.path.join(self.output_dir, 'equity_curve.csv')
        metrics_path = os.path.join(self.output_dir, 'metrics.json')
        
        trades_df.to_csv(trades_path, index=False)
        equity_df.to_csv(equity_path, index=False)
        
        with open(metrics_path, 'w', encoding='utf-8') as f:
            json.dump(metrics, f, indent=2, ensure_ascii=False)
        
        print(f"交易记录已保存到: {trades_path}")
        print(f"权益曲线已保存到: {equity_path}")
        print(f"绩效指标已保存到: {metrics_path}")
        
        # 生成可视化图表
        self.generate_visualizations(signals_df, trades_df, equity_df, metrics, params)
        
        return {
            'metrics': metrics,
            'trades_df': trades_df,
            'equity_df': equity_df,
            'signals_df': signals_df
        }
    
    def generate_visualizations(self, signals_df: pd.DataFrame, 
                              trades_df: pd.DataFrame,
                              equity_df: pd.DataFrame, 
                              metrics: dict, parameters: dict) -> None:
        """生成可视化图表"""
        print("\n生成可视化图表...")
        
        # 权益曲线图
        equity_plot_path = os.path.join(self.output_dir, 'equity_curve.png')
        self.visualizer.plot_equity_curve(equity_df, metrics, equity_plot_path)
        
        # 交易分析图
        if not trades_df.empty:
            trade_plot_path = os.path.join(self.output_dir, 'trade_analysis.png')
            self.visualizer.plot_trade_analysis(trades_df, trade_plot_path)
        
        # 信号分布图
        signal_plot_path = os.path.join(self.output_dir, 'signal_distribution.png')
        self.visualizer.plot_signal_distribution(signals_df, self.price_data, signal_plot_path)
        
        # 交互式图表
        interactive_path = os.path.join(self.output_dir, 'interactive_chart.html')
        self.visualizer.create_interactive_chart(
            self.price_data, signals_df, trades_df, interactive_path
        )
        
        # 绩效报告
        report_path = os.path.join(self.output_dir, 'performance_report.txt')
        report = self.visualizer.generate_performance_report(
            metrics, trades_df, parameters, report_path
        )
        
        print("可视化图表生成完成!")
    
    def run_parameter_optimization(self, method: str = 'grid_search', 
                                 n_trials: int = 100) -> list:
        """运行参数优化"""
        print("\n" + "=" * 60)
        print("步骤4: 参数优化")
        print("=" * 60)
        
        if self.price_data is None:
            raise ValueError("请先下载数据")
        
        # 创建目标函数
        objective_function = create_objective_function(
            self.price_data, SignalGenerator, BacktestEngine
        )
        
        # 创建优化器
        optimizer = ParameterOptimizer(
            objective_function=objective_function,
            scoring_metric='total_return',
            maximize=True,
            n_jobs=4  # 使用4个进程
        )
        
        # 定义参数范围
        if method == 'grid_search':
            param_grid = {
                'atr_period': [10, 14, 21],
                'min_shadow_atr_ratio': [1.0, 1.5, 2.0],
                'ma_period': [15, 20, 25],
                'risk_reward_ratio': [1.5, 2.0, 2.5]
            }
            results = optimizer.grid_search(param_grid)
        
        elif method == 'random_search':
            param_ranges = {
                'atr_period': (10, 30, int),
                'min_shadow_atr_ratio': (0.8, 3.0, float),
                'ma_period': (10, 50, int),
                'risk_reward_ratio': (1.0, 4.0, float)
            }
            results = optimizer.random_search(param_ranges, n_trials)
        
        else:
            raise ValueError(f"不支持的优化方法: {method}")
        
        # 保存优化结果
        optimization_path = os.path.join(self.output_dir, f'optimization_{method}.json')
        optimizer.save_results(optimization_path)
        
        # 保存结果DataFrame
        results_df = optimizer.get_results_dataframe()
        results_csv_path = os.path.join(self.output_dir, f'optimization_{method}.csv')
        results_df.to_csv(results_csv_path, index=False)
        
        print(f"优化结果已保存到: {optimization_path}")
        print(f"优化结果CSV已保存到: {results_csv_path}")
        
        # 显示最佳结果
        best_results = optimizer.get_best_parameters(5)
        print(f"\n前5个最佳参数组合:")
        for i, result in enumerate(best_results):
            print(f"第{i+1}名: 得分={result.score:.2f}, 参数={result.parameters}")
        
        return results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='针策略回测系统')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--symbol', type=str, default='BTCUSDT', help='交易对')
    parser.add_argument('--interval', type=str, default='1h', help='时间间隔')
    parser.add_argument('--start_date', type=str, default='2024-01-01', help='开始日期')
    parser.add_argument('--end_date', type=str, default='2024-06-30', help='结束日期')
    parser.add_argument('--optimize', action='store_true', help='是否进行参数优化')
    parser.add_argument('--method', type=str, default='grid_search', 
                       choices=['grid_search', 'random_search'], help='优化方法')
    
    args = parser.parse_args()
    
    # 加载配置
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
    else:
        config = {
            'symbol': args.symbol,
            'interval': args.interval,
            'start_date': args.start_date,
            'end_date': args.end_date
        }
    
    # 创建回测系统
    backtest_system = NeedleStrategyBacktest(config)
    
    try:
        # 下载数据
        backtest_system.download_data()
        
        # 分析针分布
        suggestions = backtest_system.analyze_needle_distribution()
        
        # 运行单次回测
        results = backtest_system.run_single_backtest()
        
        # 参数优化
        if args.optimize:
            optimization_results = backtest_system.run_parameter_optimization(
                method=args.method, n_trials=50
            )
        
        print("\n" + "=" * 60)
        print("回测完成!")
        print("=" * 60)
        print(f"输出目录: {backtest_system.output_dir}")
        print(f"总收益率: {results['metrics']['total_return']:.2f}%")
        print(f"胜率: {results['metrics']['win_rate']:.2f}%")
        print(f"最大回撤: {results['metrics']['max_drawdown']:.2f}%")
        
    except Exception as e:
        print(f"回测过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
