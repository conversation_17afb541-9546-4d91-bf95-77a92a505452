"""
模块测试脚本

测试所有模块的基本功能
"""
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加src目录到路径
sys.path.append('src')

def test_technical_indicators():
    """测试技术指标模块"""
    print("测试技术指标模块...")
    
    try:
        from technical_indicators import TechnicalIndicators
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=100, freq='H')
        np.random.seed(42)
        
        price = 50000
        prices = []
        for _ in range(100):
            change = np.random.normal(0, 0.01)
            price *= (1 + change)
            prices.append(price)
        
        df = pd.DataFrame(index=dates)
        df['close'] = prices
        df['open'] = df['close'].shift(1).fillna(df['close'].iloc[0])
        df['high'] = df[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.01, len(df)))
        df['low'] = df[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.01, len(df)))
        df['volume'] = np.random.uniform(100, 1000, len(df))
        
        # 测试ATR计算
        atr = TechnicalIndicators.calculate_atr(df)
        assert len(atr) == len(df), "ATR长度不匹配"
        
        # 测试移动平均
        sma = TechnicalIndicators.calculate_sma(df['close'], 20)
        assert len(sma) == len(df), "SMA长度不匹配"
        
        # 测试针指标
        needle_df = TechnicalIndicators.calculate_needle_indicator(df)
        assert 'is_needle' in needle_df.columns, "针指标列缺失"
        
        print("✓ 技术指标模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 技术指标模块测试失败: {e}")
        return False

def test_needle_indicator():
    """测试针指标模块"""
    print("测试针指标模块...")
    
    try:
        from needle_indicator import NeedleIndicator
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=100, freq='H')
        np.random.seed(42)
        
        # 创建包含针形态的数据
        data = []
        price = 50000
        
        for i in range(100):
            if i % 20 == 0:  # 每20根K线创建一个针
                open_price = price
                close_price = price + np.random.uniform(-50, 50)
                high_price = max(open_price, close_price) + np.random.uniform(300, 800)  # 长上影线
                low_price = min(open_price, close_price) - np.random.uniform(0, 50)   # 短下影线
            else:
                change = np.random.normal(0, 0.01)
                open_price = price
                close_price = price * (1 + change)
                high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.005))
                low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.005))
            
            data.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price
            })
            price = close_price
        
        df = pd.DataFrame(data, index=dates)
        df['volume'] = np.random.uniform(100, 1000, len(df))
        
        # 测试针指标
        needle_indicator = NeedleIndicator()
        result_df = needle_indicator.get_needle_signals(df)
        
        assert 'is_needle' in result_df.columns, "针指标列缺失"
        assert 'needle_strength' in result_df.columns, "针强度列缺失"
        
        # 检查是否发现了针
        needle_count = result_df['is_needle'].sum()
        print(f"发现 {needle_count} 个针形态")
        
        print("✓ 针指标模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 针指标模块测试失败: {e}")
        return False

def test_signal_generator():
    """测试信号生成模块"""
    print("测试信号生成模块...")
    
    try:
        from signal_generator import SignalGenerator
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=200, freq='H')
        np.random.seed(42)
        
        # 创建趋势性数据
        data = []
        price = 50000
        trend = 1
        
        for i in range(200):
            if i % 50 == 0:
                trend *= -1
            
            trend_change = trend * np.random.uniform(0.001, 0.003)
            noise = np.random.normal(0, 0.005)
            
            open_price = price
            close_price = price * (1 + trend_change + noise)
            
            # 偶尔创建针形态
            if i % 30 == 0 and trend == 1:
                high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.002))
                low_price = min(open_price, close_price) * (1 - np.random.uniform(0.01, 0.02))
            elif i % 35 == 0 and trend == -1:
                high_price = max(open_price, close_price) * (1 + np.random.uniform(0.01, 0.02))
                low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.002))
            else:
                high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.005))
                low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.005))
            
            data.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price
            })
            price = close_price
        
        df = pd.DataFrame(data, index=dates)
        df['volume'] = np.random.uniform(100, 1000, len(df))
        
        # 测试信号生成
        signal_generator = SignalGenerator()
        signals_df = signal_generator.generate_complete_signals(df)
        
        assert 'entry_signal' in signals_df.columns, "入场信号列缺失"
        assert 'signal_type' in signals_df.columns, "信号类型列缺失"
        
        # 检查信号数量
        signal_count = signals_df['valid_signal'].sum()
        print(f"生成 {signal_count} 个有效信号")
        
        print("✓ 信号生成模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 信号生成模块测试失败: {e}")
        return False

def test_backtest_engine():
    """测试回测引擎模块"""
    print("测试回测引擎模块...")
    
    try:
        from backtest_engine import BacktestEngine
        from signal_generator import SignalGenerator
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=100, freq='H')
        np.random.seed(42)
        
        data = []
        price = 50000
        
        for i in range(100):
            change = np.random.normal(0, 0.01)
            open_price = price
            close_price = price * (1 + change)
            high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.01))
            low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.01))
            
            data.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price
            })
            price = close_price
        
        df = pd.DataFrame(data, index=dates)
        df['volume'] = np.random.uniform(100, 1000, len(df))
        
        # 生成信号
        signal_generator = SignalGenerator()
        signals_df = signal_generator.generate_complete_signals(df)
        
        # 测试回测引擎
        backtest_engine = BacktestEngine(initial_capital=100000)
        metrics = backtest_engine.run_backtest(df, signals_df)
        
        assert 'total_return' in metrics, "总收益率指标缺失"
        assert 'win_rate' in metrics, "胜率指标缺失"
        
        print(f"回测结果: 总收益率={metrics['total_return']:.2f}%, 胜率={metrics['win_rate']:.2f}%")
        
        print("✓ 回测引擎模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 回测引擎模块测试失败: {e}")
        return False

def test_statistical_analysis():
    """测试统计分析模块"""
    print("测试统计分析模块...")
    
    try:
        from statistical_analysis import StatisticalAnalyzer
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=200, freq='H')
        np.random.seed(42)
        
        data = []
        price = 50000
        
        for i in range(200):
            if i % 30 == 0:  # 创建针形态
                open_price = price
                close_price = price + np.random.uniform(-100, 100)
                high_price = max(open_price, close_price) + np.random.uniform(500, 1000)
                low_price = min(open_price, close_price) - np.random.uniform(0, 100)
            else:
                change = np.random.normal(0, 0.01)
                open_price = price
                close_price = price * (1 + change)
                high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.005))
                low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.005))
            
            data.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price
            })
            price = close_price
        
        df = pd.DataFrame(data, index=dates)
        df['volume'] = np.random.uniform(100, 1000, len(df))
        
        # 测试统计分析
        analyzer = StatisticalAnalyzer()
        analysis_results = analyzer.analyze_needle_distribution(df)
        
        assert isinstance(analysis_results, dict), "分析结果应该是字典"
        
        # 测试参数建议
        suggestions = analyzer.suggest_parameter_ranges(analysis_results)
        assert isinstance(suggestions, dict), "参数建议应该是字典"
        
        print(f"分析了 {len(analysis_results)} 个ATR周期的数据")
        
        print("✓ 统计分析模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 统计分析模块测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("=" * 60)
    print("针策略回测系统 - 模块测试")
    print("=" * 60)
    
    tests = [
        test_technical_indicators,
        test_needle_indicator,
        test_signal_generator,
        test_backtest_engine,
        test_statistical_analysis
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 个模块测试通过")
    print("=" * 60)
    
    if passed == total:
        print("✓ 所有模块测试通过！系统可以正常运行。")
        return True
    else:
        print("✗ 部分模块测试失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    main()
