{"sourceFile": "src/statistical_analysis.py", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1752591312650, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1752591869734, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,465 @@\n+\"\"\"\n+统计分析模块\n+\n+统计针指标数值分布，为参数优化提供合理范围\n+\"\"\"\n+\n+import pandas as pd\n+import numpy as np\n+import matplotlib\n+\n+matplotlib.use(\"Agg\")  # 使用非交互式后端\n+import matplotlib.pyplot as plt\n+import seaborn as sns\n+from typing import Dict, List, Tuple, Optional\n+from scipy import stats\n+import warnings\n+\n+warnings.filterwarnings(\"ignore\")\n+\n+try:\n+    from .needle_indicator import NeedleIndicator\n+    from .technical_indicators import TechnicalIndicators\n+except ImportError:\n+    from needle_indicator import NeedleIndicator\n+    from technical_indicators import TechnicalIndicators\n+\n+\n+class StatisticalAnalyzer:\n+    \"\"\"统计分析器\"\"\"\n+\n+    def __init__(self):\n+        \"\"\"初始化统计分析器\"\"\"\n+        self.analysis_results = {}\n+\n+    def analyze_needle_distribution(\n+        self, df: pd.DataFrame, atr_periods: List[int] = [14, 21, 28]\n+    ) -> Dict:\n+        \"\"\"\n+        分析针指标分布\n+\n+        Args:\n+            df: 包含OHLC数据的DataFrame\n+            atr_periods: ATR周期列表\n+\n+        Returns:\n+            分析结果字典\n+        \"\"\"\n+        print(\"分析针指标分布...\")\n+\n+        results = {}\n+\n+        for atr_period in atr_periods:\n+            print(f\"分析ATR周期: {atr_period}\")\n+\n+            # 计算针指标\n+            needle_indicator = NeedleIndicator(atr_period=atr_period)\n+            needle_df = needle_indicator.get_needle_signals(df)\n+\n+            # 基本统计\n+            total_bars = len(needle_df)\n+            needle_bars = needle_df[needle_df[\"is_needle\"]]\n+\n+            if len(needle_bars) == 0:\n+                results[atr_period] = {\"error\": \"没有发现针形态\"}\n+                continue\n+\n+            # 针的基本统计\n+            needle_stats = {\n+                \"total_bars\": total_bars,\n+                \"needle_count\": len(needle_bars),\n+                \"needle_frequency\": len(needle_bars) / total_bars * 100,\n+                \"upper_needles\": (needle_bars[\"is_upper_needle\"]).sum(),\n+                \"lower_needles\": (needle_bars[\"is_lower_needle\"]).sum(),\n+            }\n+\n+            # 影线ATR比值分布\n+            upper_shadow_ratios = needle_df[\"upper_shadow_atr_ratio\"].dropna()\n+            lower_shadow_ratios = needle_df[\"lower_shadow_atr_ratio\"].dropna()\n+\n+            shadow_stats = {\n+                \"upper_shadow_atr_ratio\": {\n+                    \"mean\": upper_shadow_ratios.mean(),\n+                    \"std\": upper_shadow_ratios.std(),\n+                    \"min\": upper_shadow_ratios.min(),\n+                    \"max\": upper_shadow_ratios.max(),\n+                    \"percentiles\": {\n+                        \"25\": upper_shadow_ratios.quantile(0.25),\n+                        \"50\": upper_shadow_ratios.quantile(0.50),\n+                        \"75\": upper_shadow_ratios.quantile(0.75),\n+                        \"90\": upper_shadow_ratios.quantile(0.90),\n+                        \"95\": upper_shadow_ratios.quantile(0.95),\n+                    },\n+                },\n+                \"lower_shadow_atr_ratio\": {\n+                    \"mean\": lower_shadow_ratios.mean(),\n+                    \"std\": lower_shadow_ratios.std(),\n+                    \"min\": lower_shadow_ratios.min(),\n+                    \"max\": lower_shadow_ratios.max(),\n+                    \"percentiles\": {\n+                        \"25\": lower_shadow_ratios.quantile(0.25),\n+                        \"50\": lower_shadow_ratios.quantile(0.50),\n+                        \"75\": lower_shadow_ratios.quantile(0.75),\n+                        \"90\": lower_shadow_ratios.quantile(0.90),\n+                        \"95\": lower_shadow_ratios.quantile(0.95),\n+                    },\n+                },\n+            }\n+\n+            # 实体占比分布\n+            body_ratios = needle_df[\"body_ratio\"].dropna()\n+            body_stats = {\n+                \"mean\": body_ratios.mean(),\n+                \"std\": body_ratios.std(),\n+                \"min\": body_ratios.min(),\n+                \"max\": body_ratios.max(),\n+                \"percentiles\": {\n+                    \"25\": body_ratios.quantile(0.25),\n+                    \"50\": body_ratios.quantile(0.50),\n+                    \"75\": body_ratios.quantile(0.75),\n+                    \"90\": body_ratios.quantile(0.90),\n+                    \"95\": body_ratios.quantile(0.95),\n+                },\n+            }\n+\n+            # 针强度分布\n+            needle_strengths = needle_bars[\"needle_strength\"].dropna()\n+            strength_stats = {\n+                \"mean\": needle_strengths.mean(),\n+                \"std\": needle_strengths.std(),\n+                \"min\": needle_strengths.min(),\n+                \"max\": needle_strengths.max(),\n+                \"percentiles\": {\n+                    \"25\": needle_strengths.quantile(0.25),\n+                    \"50\": needle_strengths.quantile(0.50),\n+                    \"75\": needle_strengths.quantile(0.75),\n+                    \"90\": needle_strengths.quantile(0.90),\n+                    \"95\": needle_strengths.quantile(0.95),\n+                },\n+            }\n+\n+            results[atr_period] = {\n+                \"needle_stats\": needle_stats,\n+                \"shadow_stats\": shadow_stats,\n+                \"body_stats\": body_stats,\n+                \"strength_stats\": strength_stats,\n+                \"raw_data\": needle_df,\n+            }\n+\n+        self.analysis_results[\"needle_distribution\"] = results\n+        return results\n+\n+    def suggest_parameter_ranges(self, analysis_results: Dict = None) -> Dict:\n+        \"\"\"\n+        基于统计分析建议参数范围\n+\n+        Args:\n+            analysis_results: 分析结果，如果为None则使用内部结果\n+\n+        Returns:\n+            建议的参数范围字典\n+        \"\"\"\n+        if analysis_results is None:\n+            analysis_results = self.analysis_results.get(\"needle_distribution\", {})\n+\n+        if not analysis_results:\n+            return {\"error\": \"没有分析结果，请先运行analyze_needle_distribution\"}\n+\n+        suggestions = {}\n+\n+        # 分析所有ATR周期的结果\n+        all_shadow_ratios = []\n+        all_body_ratios = []\n+        all_strengths = []\n+\n+        for atr_period, results in analysis_results.items():\n+            if \"error\" in results:\n+                continue\n+\n+            # 收集影线ATR比值\n+            upper_ratios = results[\"raw_data\"][\"upper_shadow_atr_ratio\"].dropna()\n+            lower_ratios = results[\"raw_data\"][\"lower_shadow_atr_ratio\"].dropna()\n+            all_shadow_ratios.extend(upper_ratios.tolist())\n+            all_shadow_ratios.extend(lower_ratios.tolist())\n+\n+            # 收集实体占比\n+            body_ratios = results[\"raw_data\"][\"body_ratio\"].dropna()\n+            all_body_ratios.extend(body_ratios.tolist())\n+\n+            # 收集针强度\n+            needle_bars = results[\"raw_data\"][results[\"raw_data\"][\"is_needle\"]]\n+            strengths = needle_bars[\"needle_strength\"].dropna()\n+            all_strengths.extend(strengths.tolist())\n+\n+        if not all_shadow_ratios:\n+            return {\"error\": \"没有足够的数据进行分析\"}\n+\n+        # 转换为numpy数组\n+        shadow_ratios = np.array(all_shadow_ratios)\n+        body_ratios = np.array(all_body_ratios)\n+        strengths = np.array(all_strengths)\n+\n+        # 建议参数范围\n+        suggestions = {\n+            \"atr_period\": {\n+                \"range\": (10, 30),\n+                \"type\": int,\n+                \"default\": 14,\n+                \"description\": \"ATR计算周期\",\n+            },\n+            \"min_shadow_atr_ratio\": {\n+                \"range\": (\n+                    max(0.5, np.percentile(shadow_ratios, 10)),\n+                    min(5.0, np.percentile(shadow_ratios, 90)),\n+                ),\n+                \"type\": float,\n+                \"default\": max(1.0, np.percentile(shadow_ratios, 25)),\n+                \"description\": \"影线长度相对ATR的最小比值\",\n+            },\n+            \"max_body_ratio\": {\n+                \"range\": (0.1, min(0.5, np.percentile(body_ratios, 75))),\n+                \"type\": float,\n+                \"default\": min(0.3, np.percentile(body_ratios, 50)),\n+                \"description\": \"实体最大占比\",\n+            },\n+            \"max_opposite_shadow_ratio\": {\n+                \"range\": (0.05, 0.3),\n+                \"type\": float,\n+                \"default\": 0.2,\n+                \"description\": \"反向影线最大占比\",\n+            },\n+            \"min_needle_strength\": {\n+                \"range\": (\n+                    max(0.5, np.percentile(strengths, 10)),\n+                    min(3.0, np.percentile(strengths, 75)),\n+                ),\n+                \"type\": float,\n+                \"default\": max(1.0, np.percentile(strengths, 25)),\n+                \"description\": \"最小针强度\",\n+            },\n+            \"ma_period\": {\n+                \"range\": (10, 50),\n+                \"type\": int,\n+                \"default\": 20,\n+                \"description\": \"移动平均周期\",\n+            },\n+            \"risk_reward_ratio\": {\n+                \"range\": (1.0, 4.0),\n+                \"type\": float,\n+                \"default\": 2.0,\n+                \"description\": \"风险收益比\",\n+            },\n+        }\n+\n+        self.analysis_results[\"parameter_suggestions\"] = suggestions\n+        return suggestions\n+\n+    def plot_distributions(\n+        self, analysis_results: Dict = None, save_path: Optional[str] = None\n+    ) -> None:\n+        \"\"\"\n+        绘制分布图\n+\n+        Args:\n+            analysis_results: 分析结果\n+            save_path: 保存路径\n+        \"\"\"\n+        if analysis_results is None:\n+            analysis_results = self.analysis_results.get(\"needle_distribution\", {})\n+\n+        if not analysis_results:\n+            print(\"没有分析结果可以绘制\")\n+            return\n+\n+        # 设置图形样式\n+        plt.style.use(\"seaborn-v0_8\")\n+        fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n+        fig.suptitle(\"针指标分布分析\", fontsize=16, fontweight=\"bold\")\n+\n+        # 收集所有数据\n+        all_data = {\n+            \"upper_shadow_atr_ratio\": [],\n+            \"lower_shadow_atr_ratio\": [],\n+            \"body_ratio\": [],\n+            \"needle_strength\": [],\n+            \"needle_frequency\": [],\n+            \"atr_periods\": [],\n+        }\n+\n+        for atr_period, results in analysis_results.items():\n+            if \"error\" in results:\n+                continue\n+\n+            df = results[\"raw_data\"]\n+            needle_bars = df[df[\"is_needle\"]]\n+\n+            all_data[\"upper_shadow_atr_ratio\"].extend(\n+                df[\"upper_shadow_atr_ratio\"].dropna().tolist()\n+            )\n+            all_data[\"lower_shadow_atr_ratio\"].extend(\n+                df[\"lower_shadow_atr_ratio\"].dropna().tolist()\n+            )\n+            all_data[\"body_ratio\"].extend(df[\"body_ratio\"].dropna().tolist())\n+            all_data[\"needle_strength\"].extend(\n+                needle_bars[\"needle_strength\"].dropna().tolist()\n+            )\n+            all_data[\"needle_frequency\"].append(\n+                results[\"needle_stats\"][\"needle_frequency\"]\n+            )\n+            all_data[\"atr_periods\"].append(atr_period)\n+\n+        # 绘制分布图\n+        axes[0, 0].hist(\n+            all_data[\"upper_shadow_atr_ratio\"],\n+            bins=50,\n+            alpha=0.7,\n+            color=\"red\",\n+            label=\"上影线/ATR\",\n+        )\n+        axes[0, 0].hist(\n+            all_data[\"lower_shadow_atr_ratio\"],\n+            bins=50,\n+            alpha=0.7,\n+            color=\"green\",\n+            label=\"下影线/ATR\",\n+        )\n+        axes[0, 0].set_title(\"影线ATR比值分布\")\n+        axes[0, 0].set_xlabel(\"影线/ATR比值\")\n+        axes[0, 0].set_ylabel(\"频次\")\n+        axes[0, 0].legend()\n+\n+        axes[0, 1].hist(all_data[\"body_ratio\"], bins=30, alpha=0.7, color=\"blue\")\n+        axes[0, 1].set_title(\"实体占比分布\")\n+        axes[0, 1].set_xlabel(\"实体占比\")\n+        axes[0, 1].set_ylabel(\"频次\")\n+\n+        axes[0, 2].hist(all_data[\"needle_strength\"], bins=30, alpha=0.7, color=\"purple\")\n+        axes[0, 2].set_title(\"针强度分布\")\n+        axes[0, 2].set_xlabel(\"针强度\")\n+        axes[0, 2].set_ylabel(\"频次\")\n+\n+        axes[1, 0].bar(\n+            range(len(all_data[\"atr_periods\"])), all_data[\"needle_frequency\"]\n+        )\n+        axes[1, 0].set_title(\"不同ATR周期的针频率\")\n+        axes[1, 0].set_xlabel(\"ATR周期\")\n+        axes[1, 0].set_ylabel(\"针频率 (%)\")\n+        axes[1, 0].set_xticks(range(len(all_data[\"atr_periods\"])))\n+        axes[1, 0].set_xticklabels(all_data[\"atr_periods\"])\n+\n+        # 箱线图\n+        if len(all_data[\"needle_strength\"]) > 0:\n+            axes[1, 1].boxplot(\n+                [\n+                    all_data[\"upper_shadow_atr_ratio\"],\n+                    all_data[\"lower_shadow_atr_ratio\"],\n+                ],\n+                labels=[\"上影线/ATR\", \"下影线/ATR\"],\n+            )\n+            axes[1, 1].set_title(\"影线ATR比值箱线图\")\n+            axes[1, 1].set_ylabel(\"比值\")\n+\n+        axes[1, 2].boxplot(\n+            [all_data[\"body_ratio\"], all_data[\"needle_strength\"]],\n+            labels=[\"实体占比\", \"针强度\"],\n+        )\n+        axes[1, 2].set_title(\"实体占比和针强度箱线图\")\n+\n+        plt.tight_layout()\n+\n+        if save_path:\n+            plt.savefig(save_path, dpi=300, bbox_inches=\"tight\")\n+            print(f\"分布图已保存到: {save_path}\")\n+\n+        plt.show()\n+\n+    def generate_analysis_report(self, analysis_results: Dict = None) -> str:\n+        \"\"\"\n+        生成分析报告\n+\n+        Args:\n+            analysis_results: 分析结果\n+\n+        Returns:\n+            分析报告字符串\n+        \"\"\"\n+        if analysis_results is None:\n+            analysis_results = self.analysis_results.get(\"needle_distribution\", {})\n+\n+        if not analysis_results:\n+            return \"没有分析结果\"\n+\n+        report = []\n+        report.append(\"=\" * 60)\n+        report.append(\"针指标统计分析报告\")\n+        report.append(\"=\" * 60)\n+\n+        for atr_period, results in analysis_results.items():\n+            if \"error\" in results:\n+                report.append(f\"\\nATR周期 {atr_period}: {results['error']}\")\n+                continue\n+\n+            stats = results[\"needle_stats\"]\n+            shadow_stats = results[\"shadow_stats\"]\n+            body_stats = results[\"body_stats\"]\n+            strength_stats = results[\"strength_stats\"]\n+\n+            report.append(f\"\\n--- ATR周期: {atr_period} ---\")\n+            report.append(f\"总K线数: {stats['total_bars']}\")\n+            report.append(f\"针数量: {stats['needle_count']}\")\n+            report.append(f\"针频率: {stats['needle_frequency']:.2f}%\")\n+            report.append(f\"上针数量: {stats['upper_needles']}\")\n+            report.append(f\"下针数量: {stats['lower_needles']}\")\n+\n+            report.append(f\"\\n影线ATR比值统计:\")\n+            report.append(\n+                f\"  上影线 - 均值: {shadow_stats['upper_shadow_atr_ratio']['mean']:.2f}, \"\n+                f\"标准差: {shadow_stats['upper_shadow_atr_ratio']['std']:.2f}\"\n+            )\n+            report.append(\n+                f\"  下影线 - 均值: {shadow_stats['lower_shadow_atr_ratio']['mean']:.2f}, \"\n+                f\"标准差: {shadow_stats['lower_shadow_atr_ratio']['std']:.2f}\"\n+            )\n+\n+            report.append(f\"\\n实体占比统计:\")\n+            report.append(\n+                f\"  均值: {body_stats['mean']:.3f}, 标准差: {body_stats['std']:.3f}\"\n+            )\n+            report.append(f\"  75%分位数: {body_stats['percentiles']['75']:.3f}\")\n+\n+            report.append(f\"\\n针强度统计:\")\n+            report.append(\n+                f\"  均值: {strength_stats['mean']:.2f}, 标准差: {strength_stats['std']:.2f}\"\n+            )\n+            report.append(f\"  25%分位数: {strength_stats['percentiles']['25']:.2f}\")\n+            report.append(f\"  75%分位数: {strength_stats['percentiles']['75']:.2f}\")\n+\n+        # 添加参数建议\n+        suggestions = self.analysis_results.get(\"parameter_suggestions\", {})\n+        if suggestions and \"error\" not in suggestions:\n+            report.append(f\"\\n{'='*60}\")\n+            report.append(\"参数优化建议\")\n+            report.append(\"=\" * 60)\n+\n+            for param, info in suggestions.items():\n+                if isinstance(info[\"range\"], tuple):\n+                    range_str = f\"({info['range'][0]:.2f}, {info['range'][1]:.2f})\"\n+                else:\n+                    range_str = str(info[\"range\"])\n+\n+                report.append(f\"{param}:\")\n+                report.append(f\"  建议范围: {range_str}\")\n+                report.append(f\"  默认值: {info['default']}\")\n+                report.append(f\"  说明: {info['description']}\")\n+\n+        return \"\\n\".join(report)\n+\n+\n+def main():\n+    \"\"\"测试统计分析功能\"\"\"\n+    print(\"统计分析模块已创建\")\n+    print(\"请在主程序中使用完整的数据进行测试\")\n+\n+\n+if __name__ == \"__main__\":\n+    main()\n"}], "date": 1752591312650, "name": "Commit-0", "content": "\"\"\"\n统计分析模块\n\n统计针指标数值分布，为参数优化提供合理范围\n\"\"\"\n\nimport pandas as pd\nimport numpy as np\nimport matplotlib\n\nmatplotlib.use(\"Agg\")  # 使用非交互式后端\nimport matplotlib.pyplot as plt\nimport seaborn as sns\nfrom typing import Dict, List, Tuple, Optional\nfrom scipy import stats\nimport warnings\n\nwarnings.filterwarnings(\"ignore\")\n\nfrom .needle_indicator import NeedleIndicator\nfrom .technical_indicators import TechnicalIndicators\n\n\nclass StatisticalAnalyzer:\n    \"\"\"统计分析器\"\"\"\n\n    def __init__(self):\n        \"\"\"初始化统计分析器\"\"\"\n        self.analysis_results = {}\n\n    def analyze_needle_distribution(\n        self, df: pd.DataFrame, atr_periods: List[int] = [14, 21, 28]\n    ) -> Dict:\n        \"\"\"\n        分析针指标分布\n\n        Args:\n            df: 包含OHLC数据的DataFrame\n            atr_periods: ATR周期列表\n\n        Returns:\n            分析结果字典\n        \"\"\"\n        print(\"分析针指标分布...\")\n\n        results = {}\n\n        for atr_period in atr_periods:\n            print(f\"分析ATR周期: {atr_period}\")\n\n            # 计算针指标\n            needle_indicator = NeedleIndicator(atr_period=atr_period)\n            needle_df = needle_indicator.get_needle_signals(df)\n\n            # 基本统计\n            total_bars = len(needle_df)\n            needle_bars = needle_df[needle_df[\"is_needle\"]]\n\n            if len(needle_bars) == 0:\n                results[atr_period] = {\"error\": \"没有发现针形态\"}\n                continue\n\n            # 针的基本统计\n            needle_stats = {\n                \"total_bars\": total_bars,\n                \"needle_count\": len(needle_bars),\n                \"needle_frequency\": len(needle_bars) / total_bars * 100,\n                \"upper_needles\": (needle_bars[\"is_upper_needle\"]).sum(),\n                \"lower_needles\": (needle_bars[\"is_lower_needle\"]).sum(),\n            }\n\n            # 影线ATR比值分布\n            upper_shadow_ratios = needle_df[\"upper_shadow_atr_ratio\"].dropna()\n            lower_shadow_ratios = needle_df[\"lower_shadow_atr_ratio\"].dropna()\n\n            shadow_stats = {\n                \"upper_shadow_atr_ratio\": {\n                    \"mean\": upper_shadow_ratios.mean(),\n                    \"std\": upper_shadow_ratios.std(),\n                    \"min\": upper_shadow_ratios.min(),\n                    \"max\": upper_shadow_ratios.max(),\n                    \"percentiles\": {\n                        \"25\": upper_shadow_ratios.quantile(0.25),\n                        \"50\": upper_shadow_ratios.quantile(0.50),\n                        \"75\": upper_shadow_ratios.quantile(0.75),\n                        \"90\": upper_shadow_ratios.quantile(0.90),\n                        \"95\": upper_shadow_ratios.quantile(0.95),\n                    },\n                },\n                \"lower_shadow_atr_ratio\": {\n                    \"mean\": lower_shadow_ratios.mean(),\n                    \"std\": lower_shadow_ratios.std(),\n                    \"min\": lower_shadow_ratios.min(),\n                    \"max\": lower_shadow_ratios.max(),\n                    \"percentiles\": {\n                        \"25\": lower_shadow_ratios.quantile(0.25),\n                        \"50\": lower_shadow_ratios.quantile(0.50),\n                        \"75\": lower_shadow_ratios.quantile(0.75),\n                        \"90\": lower_shadow_ratios.quantile(0.90),\n                        \"95\": lower_shadow_ratios.quantile(0.95),\n                    },\n                },\n            }\n\n            # 实体占比分布\n            body_ratios = needle_df[\"body_ratio\"].dropna()\n            body_stats = {\n                \"mean\": body_ratios.mean(),\n                \"std\": body_ratios.std(),\n                \"min\": body_ratios.min(),\n                \"max\": body_ratios.max(),\n                \"percentiles\": {\n                    \"25\": body_ratios.quantile(0.25),\n                    \"50\": body_ratios.quantile(0.50),\n                    \"75\": body_ratios.quantile(0.75),\n                    \"90\": body_ratios.quantile(0.90),\n                    \"95\": body_ratios.quantile(0.95),\n                },\n            }\n\n            # 针强度分布\n            needle_strengths = needle_bars[\"needle_strength\"].dropna()\n            strength_stats = {\n                \"mean\": needle_strengths.mean(),\n                \"std\": needle_strengths.std(),\n                \"min\": needle_strengths.min(),\n                \"max\": needle_strengths.max(),\n                \"percentiles\": {\n                    \"25\": needle_strengths.quantile(0.25),\n                    \"50\": needle_strengths.quantile(0.50),\n                    \"75\": needle_strengths.quantile(0.75),\n                    \"90\": needle_strengths.quantile(0.90),\n                    \"95\": needle_strengths.quantile(0.95),\n                },\n            }\n\n            results[atr_period] = {\n                \"needle_stats\": needle_stats,\n                \"shadow_stats\": shadow_stats,\n                \"body_stats\": body_stats,\n                \"strength_stats\": strength_stats,\n                \"raw_data\": needle_df,\n            }\n\n        self.analysis_results[\"needle_distribution\"] = results\n        return results\n\n    def suggest_parameter_ranges(self, analysis_results: Dict = None) -> Dict:\n        \"\"\"\n        基于统计分析建议参数范围\n\n        Args:\n            analysis_results: 分析结果，如果为None则使用内部结果\n\n        Returns:\n            建议的参数范围字典\n        \"\"\"\n        if analysis_results is None:\n            analysis_results = self.analysis_results.get(\"needle_distribution\", {})\n\n        if not analysis_results:\n            return {\"error\": \"没有分析结果，请先运行analyze_needle_distribution\"}\n\n        suggestions = {}\n\n        # 分析所有ATR周期的结果\n        all_shadow_ratios = []\n        all_body_ratios = []\n        all_strengths = []\n\n        for atr_period, results in analysis_results.items():\n            if \"error\" in results:\n                continue\n\n            # 收集影线ATR比值\n            upper_ratios = results[\"raw_data\"][\"upper_shadow_atr_ratio\"].dropna()\n            lower_ratios = results[\"raw_data\"][\"lower_shadow_atr_ratio\"].dropna()\n            all_shadow_ratios.extend(upper_ratios.tolist())\n            all_shadow_ratios.extend(lower_ratios.tolist())\n\n            # 收集实体占比\n            body_ratios = results[\"raw_data\"][\"body_ratio\"].dropna()\n            all_body_ratios.extend(body_ratios.tolist())\n\n            # 收集针强度\n            needle_bars = results[\"raw_data\"][results[\"raw_data\"][\"is_needle\"]]\n            strengths = needle_bars[\"needle_strength\"].dropna()\n            all_strengths.extend(strengths.tolist())\n\n        if not all_shadow_ratios:\n            return {\"error\": \"没有足够的数据进行分析\"}\n\n        # 转换为numpy数组\n        shadow_ratios = np.array(all_shadow_ratios)\n        body_ratios = np.array(all_body_ratios)\n        strengths = np.array(all_strengths)\n\n        # 建议参数范围\n        suggestions = {\n            \"atr_period\": {\n                \"range\": (10, 30),\n                \"type\": int,\n                \"default\": 14,\n                \"description\": \"ATR计算周期\",\n            },\n            \"min_shadow_atr_ratio\": {\n                \"range\": (\n                    max(0.5, np.percentile(shadow_ratios, 10)),\n                    min(5.0, np.percentile(shadow_ratios, 90)),\n                ),\n                \"type\": float,\n                \"default\": max(1.0, np.percentile(shadow_ratios, 25)),\n                \"description\": \"影线长度相对ATR的最小比值\",\n            },\n            \"max_body_ratio\": {\n                \"range\": (0.1, min(0.5, np.percentile(body_ratios, 75))),\n                \"type\": float,\n                \"default\": min(0.3, np.percentile(body_ratios, 50)),\n                \"description\": \"实体最大占比\",\n            },\n            \"max_opposite_shadow_ratio\": {\n                \"range\": (0.05, 0.3),\n                \"type\": float,\n                \"default\": 0.2,\n                \"description\": \"反向影线最大占比\",\n            },\n            \"min_needle_strength\": {\n                \"range\": (\n                    max(0.5, np.percentile(strengths, 10)),\n                    min(3.0, np.percentile(strengths, 75)),\n                ),\n                \"type\": float,\n                \"default\": max(1.0, np.percentile(strengths, 25)),\n                \"description\": \"最小针强度\",\n            },\n            \"ma_period\": {\n                \"range\": (10, 50),\n                \"type\": int,\n                \"default\": 20,\n                \"description\": \"移动平均周期\",\n            },\n            \"risk_reward_ratio\": {\n                \"range\": (1.0, 4.0),\n                \"type\": float,\n                \"default\": 2.0,\n                \"description\": \"风险收益比\",\n            },\n        }\n\n        self.analysis_results[\"parameter_suggestions\"] = suggestions\n        return suggestions\n\n    def plot_distributions(\n        self, analysis_results: Dict = None, save_path: Optional[str] = None\n    ) -> None:\n        \"\"\"\n        绘制分布图\n\n        Args:\n            analysis_results: 分析结果\n            save_path: 保存路径\n        \"\"\"\n        if analysis_results is None:\n            analysis_results = self.analysis_results.get(\"needle_distribution\", {})\n\n        if not analysis_results:\n            print(\"没有分析结果可以绘制\")\n            return\n\n        # 设置图形样式\n        plt.style.use(\"seaborn-v0_8\")\n        fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n        fig.suptitle(\"针指标分布分析\", fontsize=16, fontweight=\"bold\")\n\n        # 收集所有数据\n        all_data = {\n            \"upper_shadow_atr_ratio\": [],\n            \"lower_shadow_atr_ratio\": [],\n            \"body_ratio\": [],\n            \"needle_strength\": [],\n            \"needle_frequency\": [],\n            \"atr_periods\": [],\n        }\n\n        for atr_period, results in analysis_results.items():\n            if \"error\" in results:\n                continue\n\n            df = results[\"raw_data\"]\n            needle_bars = df[df[\"is_needle\"]]\n\n            all_data[\"upper_shadow_atr_ratio\"].extend(\n                df[\"upper_shadow_atr_ratio\"].dropna().tolist()\n            )\n            all_data[\"lower_shadow_atr_ratio\"].extend(\n                df[\"lower_shadow_atr_ratio\"].dropna().tolist()\n            )\n            all_data[\"body_ratio\"].extend(df[\"body_ratio\"].dropna().tolist())\n            all_data[\"needle_strength\"].extend(\n                needle_bars[\"needle_strength\"].dropna().tolist()\n            )\n            all_data[\"needle_frequency\"].append(\n                results[\"needle_stats\"][\"needle_frequency\"]\n            )\n            all_data[\"atr_periods\"].append(atr_period)\n\n        # 绘制分布图\n        axes[0, 0].hist(\n            all_data[\"upper_shadow_atr_ratio\"],\n            bins=50,\n            alpha=0.7,\n            color=\"red\",\n            label=\"上影线/ATR\",\n        )\n        axes[0, 0].hist(\n            all_data[\"lower_shadow_atr_ratio\"],\n            bins=50,\n            alpha=0.7,\n            color=\"green\",\n            label=\"下影线/ATR\",\n        )\n        axes[0, 0].set_title(\"影线ATR比值分布\")\n        axes[0, 0].set_xlabel(\"影线/ATR比值\")\n        axes[0, 0].set_ylabel(\"频次\")\n        axes[0, 0].legend()\n\n        axes[0, 1].hist(all_data[\"body_ratio\"], bins=30, alpha=0.7, color=\"blue\")\n        axes[0, 1].set_title(\"实体占比分布\")\n        axes[0, 1].set_xlabel(\"实体占比\")\n        axes[0, 1].set_ylabel(\"频次\")\n\n        axes[0, 2].hist(all_data[\"needle_strength\"], bins=30, alpha=0.7, color=\"purple\")\n        axes[0, 2].set_title(\"针强度分布\")\n        axes[0, 2].set_xlabel(\"针强度\")\n        axes[0, 2].set_ylabel(\"频次\")\n\n        axes[1, 0].bar(\n            range(len(all_data[\"atr_periods\"])), all_data[\"needle_frequency\"]\n        )\n        axes[1, 0].set_title(\"不同ATR周期的针频率\")\n        axes[1, 0].set_xlabel(\"ATR周期\")\n        axes[1, 0].set_ylabel(\"针频率 (%)\")\n        axes[1, 0].set_xticks(range(len(all_data[\"atr_periods\"])))\n        axes[1, 0].set_xticklabels(all_data[\"atr_periods\"])\n\n        # 箱线图\n        if len(all_data[\"needle_strength\"]) > 0:\n            axes[1, 1].boxplot(\n                [\n                    all_data[\"upper_shadow_atr_ratio\"],\n                    all_data[\"lower_shadow_atr_ratio\"],\n                ],\n                labels=[\"上影线/ATR\", \"下影线/ATR\"],\n            )\n            axes[1, 1].set_title(\"影线ATR比值箱线图\")\n            axes[1, 1].set_ylabel(\"比值\")\n\n        axes[1, 2].boxplot(\n            [all_data[\"body_ratio\"], all_data[\"needle_strength\"]],\n            labels=[\"实体占比\", \"针强度\"],\n        )\n        axes[1, 2].set_title(\"实体占比和针强度箱线图\")\n\n        plt.tight_layout()\n\n        if save_path:\n            plt.savefig(save_path, dpi=300, bbox_inches=\"tight\")\n            print(f\"分布图已保存到: {save_path}\")\n\n        plt.show()\n\n    def generate_analysis_report(self, analysis_results: Dict = None) -> str:\n        \"\"\"\n        生成分析报告\n\n        Args:\n            analysis_results: 分析结果\n\n        Returns:\n            分析报告字符串\n        \"\"\"\n        if analysis_results is None:\n            analysis_results = self.analysis_results.get(\"needle_distribution\", {})\n\n        if not analysis_results:\n            return \"没有分析结果\"\n\n        report = []\n        report.append(\"=\" * 60)\n        report.append(\"针指标统计分析报告\")\n        report.append(\"=\" * 60)\n\n        for atr_period, results in analysis_results.items():\n            if \"error\" in results:\n                report.append(f\"\\nATR周期 {atr_period}: {results['error']}\")\n                continue\n\n            stats = results[\"needle_stats\"]\n            shadow_stats = results[\"shadow_stats\"]\n            body_stats = results[\"body_stats\"]\n            strength_stats = results[\"strength_stats\"]\n\n            report.append(f\"\\n--- ATR周期: {atr_period} ---\")\n            report.append(f\"总K线数: {stats['total_bars']}\")\n            report.append(f\"针数量: {stats['needle_count']}\")\n            report.append(f\"针频率: {stats['needle_frequency']:.2f}%\")\n            report.append(f\"上针数量: {stats['upper_needles']}\")\n            report.append(f\"下针数量: {stats['lower_needles']}\")\n\n            report.append(f\"\\n影线ATR比值统计:\")\n            report.append(\n                f\"  上影线 - 均值: {shadow_stats['upper_shadow_atr_ratio']['mean']:.2f}, \"\n                f\"标准差: {shadow_stats['upper_shadow_atr_ratio']['std']:.2f}\"\n            )\n            report.append(\n                f\"  下影线 - 均值: {shadow_stats['lower_shadow_atr_ratio']['mean']:.2f}, \"\n                f\"标准差: {shadow_stats['lower_shadow_atr_ratio']['std']:.2f}\"\n            )\n\n            report.append(f\"\\n实体占比统计:\")\n            report.append(\n                f\"  均值: {body_stats['mean']:.3f}, 标准差: {body_stats['std']:.3f}\"\n            )\n            report.append(f\"  75%分位数: {body_stats['percentiles']['75']:.3f}\")\n\n            report.append(f\"\\n针强度统计:\")\n            report.append(\n                f\"  均值: {strength_stats['mean']:.2f}, 标准差: {strength_stats['std']:.2f}\"\n            )\n            report.append(f\"  25%分位数: {strength_stats['percentiles']['25']:.2f}\")\n            report.append(f\"  75%分位数: {strength_stats['percentiles']['75']:.2f}\")\n\n        # 添加参数建议\n        suggestions = self.analysis_results.get(\"parameter_suggestions\", {})\n        if suggestions and \"error\" not in suggestions:\n            report.append(f\"\\n{'='*60}\")\n            report.append(\"参数优化建议\")\n            report.append(\"=\" * 60)\n\n            for param, info in suggestions.items():\n                if isinstance(info[\"range\"], tuple):\n                    range_str = f\"({info['range'][0]:.2f}, {info['range'][1]:.2f})\"\n                else:\n                    range_str = str(info[\"range\"])\n\n                report.append(f\"{param}:\")\n                report.append(f\"  建议范围: {range_str}\")\n                report.append(f\"  默认值: {info['default']}\")\n                report.append(f\"  说明: {info['description']}\")\n\n        return \"\\n\".join(report)\n\n\ndef main():\n    \"\"\"测试统计分析功能\"\"\"\n    print(\"统计分析模块已创建\")\n    print(\"请在主程序中使用完整的数据进行测试\")\n\n\nif __name__ == \"__main__\":\n    main()\n"}]}