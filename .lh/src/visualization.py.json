{"sourceFile": "src/visualization.py", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1752591326245, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1752591326245, "name": "Commit-0", "content": "\"\"\"\n可视化与报告模块\n\n生成收益曲线、回撤图、信号分布图等，输出详细的回测报告\n\"\"\"\n\nimport pandas as pd\nimport numpy as np\nimport matplotlib\n\nmatplotlib.use(\"Agg\")  # 使用非交互式后端\nimport matplotlib.pyplot as plt\nimport matplotlib.dates as mdates\nimport seaborn as sns\nimport plotly.graph_objects as go\nimport plotly.subplots as sp\nfrom plotly.offline import plot\nfrom typing import Dict, List, Optional, Tuple\nimport os\nfrom datetime import datetime\nimport warnings\n\nwarnings.filterwarnings(\"ignore\")\n\n\nclass BacktestVisualizer:\n    \"\"\"回测可视化器\"\"\"\n\n    def __init__(self, output_dir: str = \"output\"):\n        \"\"\"\n        初始化可视化器\n\n        Args:\n            output_dir: 输出目录\n        \"\"\"\n        self.output_dir = output_dir\n        os.makedirs(output_dir, exist_ok=True)\n\n        # 设置中文字体\n        plt.rcParams[\"font.sans-serif\"] = [\"SimHei\", \"Arial Unicode MS\", \"DejaVu Sans\"]\n        plt.rcParams[\"axes.unicode_minus\"] = False\n\n    def plot_equity_curve(\n        self, equity_df: pd.DataFrame, metrics: Dict, save_path: Optional[str] = None\n    ) -> None:\n        \"\"\"\n        绘制权益曲线\n\n        Args:\n            equity_df: 权益曲线DataFrame\n            metrics: 绩效指标\n            save_path: 保存路径\n        \"\"\"\n        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))\n\n        # 权益曲线\n        ax1.plot(\n            equity_df[\"timestamp\"],\n            equity_df[\"total_equity\"],\n            linewidth=2,\n            color=\"blue\",\n            label=\"总权益\",\n        )\n        ax1.plot(\n            equity_df[\"timestamp\"],\n            equity_df[\"capital\"],\n            linewidth=1,\n            color=\"green\",\n            alpha=0.7,\n            label=\"已实现资金\",\n        )\n\n        ax1.set_title(\n            f'权益曲线 - 总收益: {metrics[\"total_return\"]:.2f}%, '\n            f'最大回撤: {metrics[\"max_drawdown\"]:.2f}%',\n            fontsize=14,\n        )\n        ax1.set_ylabel(\"权益 ($)\")\n        ax1.legend()\n        ax1.grid(True, alpha=0.3)\n\n        # 回撤图\n        peak = equity_df[\"total_equity\"].expanding().max()\n        drawdown = (equity_df[\"total_equity\"] - peak) / peak * 100\n\n        ax2.fill_between(\n            equity_df[\"timestamp\"], drawdown, 0, color=\"red\", alpha=0.3, label=\"回撤\"\n        )\n        ax2.plot(equity_df[\"timestamp\"], drawdown, color=\"red\", linewidth=1)\n\n        ax2.set_title(\"回撤曲线\")\n        ax2.set_xlabel(\"时间\")\n        ax2.set_ylabel(\"回撤 (%)\")\n        ax2.legend()\n        ax2.grid(True, alpha=0.3)\n\n        # 格式化x轴\n        for ax in [ax1, ax2]:\n            ax.xaxis.set_major_formatter(mdates.DateFormatter(\"%Y-%m-%d\"))\n            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))\n            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)\n\n        plt.tight_layout()\n\n        if save_path:\n            plt.savefig(save_path, dpi=300, bbox_inches=\"tight\")\n            print(f\"权益曲线图已保存到: {save_path}\")\n\n        plt.show()\n\n    def plot_trade_analysis(\n        self, trades_df: pd.DataFrame, save_path: Optional[str] = None\n    ) -> None:\n        \"\"\"\n        绘制交易分析图\n\n        Args:\n            trades_df: 交易DataFrame\n            save_path: 保存路径\n        \"\"\"\n        if trades_df.empty:\n            print(\"没有交易数据可以绘制\")\n            return\n\n        fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n\n        # 盈亏分布\n        axes[0, 0].hist(\n            trades_df[\"pnl\"],\n            bins=30,\n            alpha=0.7,\n            color=[\"red\" if x < 0 else \"green\" for x in trades_df[\"pnl\"]],\n        )\n        axes[0, 0].set_title(\"盈亏分布\")\n        axes[0, 0].set_xlabel(\"盈亏 ($)\")\n        axes[0, 0].set_ylabel(\"交易次数\")\n        axes[0, 0].axvline(x=0, color=\"black\", linestyle=\"--\", alpha=0.5)\n\n        # 盈亏百分比分布\n        axes[0, 1].hist(\n            trades_df[\"pnl_pct\"],\n            bins=30,\n            alpha=0.7,\n            color=[\"red\" if x < 0 else \"green\" for x in trades_df[\"pnl_pct\"]],\n        )\n        axes[0, 1].set_title(\"盈亏百分比分布\")\n        axes[0, 1].set_xlabel(\"盈亏百分比 (%)\")\n        axes[0, 1].set_ylabel(\"交易次数\")\n        axes[0, 1].axvline(x=0, color=\"black\", linestyle=\"--\", alpha=0.5)\n\n        # 交易类型分布\n        signal_counts = trades_df[\"signal_type\"].value_counts()\n        axes[1, 0].pie(\n            signal_counts.values, labels=signal_counts.index, autopct=\"%1.1f%%\"\n        )\n        axes[1, 0].set_title(\"交易类型分布\")\n\n        # 交易状态分布\n        status_counts = trades_df[\"status\"].value_counts()\n        axes[1, 1].bar(status_counts.index, status_counts.values)\n        axes[1, 1].set_title(\"交易状态分布\")\n        axes[1, 1].set_xlabel(\"状态\")\n        axes[1, 1].set_ylabel(\"交易次数\")\n        plt.setp(axes[1, 1].xaxis.get_majorticklabels(), rotation=45)\n\n        plt.tight_layout()\n\n        if save_path:\n            plt.savefig(save_path, dpi=300, bbox_inches=\"tight\")\n            print(f\"交易分析图已保存到: {save_path}\")\n\n        plt.show()\n\n    def plot_signal_distribution(\n        self,\n        signals_df: pd.DataFrame,\n        price_df: pd.DataFrame,\n        save_path: Optional[str] = None,\n    ) -> None:\n        \"\"\"\n        绘制信号分布图\n\n        Args:\n            signals_df: 信号DataFrame\n            price_df: 价格DataFrame\n            save_path: 保存路径\n        \"\"\"\n        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))\n\n        # 价格和信号\n        ax1.plot(\n            price_df.index, price_df[\"close\"], linewidth=1, color=\"black\", alpha=0.7\n        )\n\n        # 标记信号点\n        long_signals = signals_df[signals_df[\"signal_type\"] == \"long\"]\n        short_signals = signals_df[signals_df[\"signal_type\"] == \"short\"]\n\n        if not long_signals.empty:\n            ax1.scatter(\n                long_signals.index,\n                long_signals[\"close\"],\n                color=\"green\",\n                marker=\"^\",\n                s=50,\n                label=\"做多信号\",\n                zorder=5,\n            )\n\n        if not short_signals.empty:\n            ax1.scatter(\n                short_signals.index,\n                short_signals[\"close\"],\n                color=\"red\",\n                marker=\"v\",\n                s=50,\n                label=\"做空信号\",\n                zorder=5,\n            )\n\n        ax1.set_title(\"价格走势与交易信号\")\n        ax1.set_ylabel(\"价格\")\n        ax1.legend()\n        ax1.grid(True, alpha=0.3)\n\n        # 针强度分布\n        needle_data = signals_df[signals_df[\"is_needle\"]]\n        if not needle_data.empty:\n            ax2.scatter(\n                needle_data.index,\n                needle_data[\"needle_strength\"],\n                c=[\n                    \"red\" if x == \"upper\" else \"green\"\n                    for x in needle_data[\"needle_type\"]\n                ],\n                alpha=0.6,\n                s=30,\n            )\n            ax2.set_title(\"针强度分布\")\n            ax2.set_xlabel(\"时间\")\n            ax2.set_ylabel(\"针强度\")\n            ax2.grid(True, alpha=0.3)\n\n        # 格式化x轴\n        for ax in [ax1, ax2]:\n            ax.xaxis.set_major_formatter(mdates.DateFormatter(\"%Y-%m-%d\"))\n            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))\n            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)\n\n        plt.tight_layout()\n\n        if save_path:\n            plt.savefig(save_path, dpi=300, bbox_inches=\"tight\")\n            print(f\"信号分布图已保存到: {save_path}\")\n\n        plt.show()\n\n    def create_interactive_chart(\n        self,\n        price_df: pd.DataFrame,\n        signals_df: pd.DataFrame,\n        trades_df: pd.DataFrame,\n        save_path: Optional[str] = None,\n    ) -> None:\n        \"\"\"\n        创建交互式图表\n\n        Args:\n            price_df: 价格DataFrame\n            signals_df: 信号DataFrame\n            trades_df: 交易DataFrame\n            save_path: 保存路径\n        \"\"\"\n        # 创建子图\n        fig = sp.make_subplots(\n            rows=3,\n            cols=1,\n            subplot_titles=(\"价格走势与交易信号\", \"针强度\", \"累计盈亏\"),\n            vertical_spacing=0.08,\n            row_heights=[0.5, 0.25, 0.25],\n        )\n\n        # 价格走势\n        fig.add_trace(\n            go.Candlestick(\n                x=price_df.index,\n                open=price_df[\"open\"],\n                high=price_df[\"high\"],\n                low=price_df[\"low\"],\n                close=price_df[\"close\"],\n                name=\"价格\",\n            ),\n            row=1,\n            col=1,\n        )\n\n        # 交易信号\n        long_signals = signals_df[signals_df[\"signal_type\"] == \"long\"]\n        short_signals = signals_df[signals_df[\"signal_type\"] == \"short\"]\n\n        if not long_signals.empty:\n            fig.add_trace(\n                go.Scatter(\n                    x=long_signals.index,\n                    y=long_signals[\"close\"],\n                    mode=\"markers\",\n                    marker=dict(symbol=\"triangle-up\", size=10, color=\"green\"),\n                    name=\"做多信号\",\n                ),\n                row=1,\n                col=1,\n            )\n\n        if not short_signals.empty:\n            fig.add_trace(\n                go.Scatter(\n                    x=short_signals.index,\n                    y=short_signals[\"close\"],\n                    mode=\"markers\",\n                    marker=dict(symbol=\"triangle-down\", size=10, color=\"red\"),\n                    name=\"做空信号\",\n                ),\n                row=1,\n                col=1,\n            )\n\n        # 针强度\n        needle_data = signals_df[signals_df[\"is_needle\"]]\n        if not needle_data.empty:\n            colors = [\n                \"red\" if x == \"upper\" else \"green\" for x in needle_data[\"needle_type\"]\n            ]\n            fig.add_trace(\n                go.Scatter(\n                    x=needle_data.index,\n                    y=needle_data[\"needle_strength\"],\n                    mode=\"markers\",\n                    marker=dict(color=colors, size=6),\n                    name=\"针强度\",\n                ),\n                row=2,\n                col=1,\n            )\n\n        # 累计盈亏\n        if not trades_df.empty:\n            cumulative_pnl = trades_df[\"pnl\"].cumsum()\n            fig.add_trace(\n                go.Scatter(\n                    x=trades_df[\"exit_time\"],\n                    y=cumulative_pnl,\n                    mode=\"lines\",\n                    line=dict(color=\"blue\", width=2),\n                    name=\"累计盈亏\",\n                ),\n                row=3,\n                col=1,\n            )\n\n        # 更新布局\n        fig.update_layout(\n            title=\"针策略回测分析\",\n            height=800,\n            showlegend=True,\n            xaxis_rangeslider_visible=False,\n        )\n\n        # 保存或显示\n        if save_path:\n            plot(fig, filename=save_path, auto_open=False)\n            print(f\"交互式图表已保存到: {save_path}\")\n        else:\n            fig.show()\n\n    def generate_performance_report(\n        self,\n        metrics: Dict,\n        trades_df: pd.DataFrame,\n        parameters: Dict,\n        save_path: Optional[str] = None,\n    ) -> str:\n        \"\"\"\n        生成绩效报告\n\n        Args:\n            metrics: 绩效指标\n            trades_df: 交易DataFrame\n            parameters: 策略参数\n            save_path: 保存路径\n\n        Returns:\n            报告字符串\n        \"\"\"\n        report = []\n        report.append(\"=\" * 80)\n        report.append(\"针策略回测绩效报告\")\n        report.append(\"=\" * 80)\n        report.append(f\"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n        report.append(\"\")\n\n        # 策略参数\n        report.append(\"策略参数:\")\n        report.append(\"-\" * 40)\n        for key, value in parameters.items():\n            report.append(f\"{key}: {value}\")\n        report.append(\"\")\n\n        # 基本绩效指标\n        report.append(\"基本绩效指标:\")\n        report.append(\"-\" * 40)\n        report.append(f\"总交易次数: {metrics.get('total_trades', 0)}\")\n        report.append(f\"胜率: {metrics.get('win_rate', 0):.2f}%\")\n        report.append(f\"总收益率: {metrics.get('total_return', 0):.2f}%\")\n        report.append(f\"总盈亏: ${metrics.get('total_pnl', 0):,.2f}\")\n        report.append(f\"最大回撤: {metrics.get('max_drawdown', 0):.2f}%\")\n        report.append(f\"夏普比率: {metrics.get('sharpe_ratio', 0):.3f}\")\n        report.append(f\"盈亏比: {metrics.get('profit_factor', 0):.2f}\")\n        report.append(\"\")\n\n        # 交易统计\n        if not trades_df.empty:\n            winning_trades = trades_df[trades_df[\"pnl\"] > 0]\n            losing_trades = trades_df[trades_df[\"pnl\"] < 0]\n\n            report.append(\"交易统计:\")\n            report.append(\"-\" * 40)\n            report.append(f\"盈利交易: {len(winning_trades)}\")\n            report.append(f\"亏损交易: {len(losing_trades)}\")\n            report.append(\n                f\"平均盈利: ${winning_trades['pnl'].mean():.2f}\"\n                if len(winning_trades) > 0\n                else \"平均盈利: $0.00\"\n            )\n            report.append(\n                f\"平均亏损: ${losing_trades['pnl'].mean():.2f}\"\n                if len(losing_trades) > 0\n                else \"平均亏损: $0.00\"\n            )\n            report.append(f\"最大单笔盈利: ${trades_df['pnl'].max():.2f}\")\n            report.append(f\"最大单笔亏损: ${trades_df['pnl'].min():.2f}\")\n            report.append(\"\")\n\n            # 交易类型分析\n            signal_stats = trades_df[\"signal_type\"].value_counts()\n            report.append(\"交易类型分析:\")\n            report.append(\"-\" * 40)\n            for signal_type, count in signal_stats.items():\n                type_trades = trades_df[trades_df[\"signal_type\"] == signal_type]\n                type_pnl = type_trades[\"pnl\"].sum()\n                type_win_rate = (type_trades[\"pnl\"] > 0).mean() * 100\n                report.append(\n                    f\"{signal_type}: {count}笔, 盈亏: ${type_pnl:.2f}, 胜率: {type_win_rate:.1f}%\"\n                )\n\n        report_text = \"\\n\".join(report)\n\n        if save_path:\n            with open(save_path, \"w\", encoding=\"utf-8\") as f:\n                f.write(report_text)\n            print(f\"绩效报告已保存到: {save_path}\")\n\n        return report_text\n\n\ndef main():\n    \"\"\"测试可视化功能\"\"\"\n    print(\"可视化模块已创建\")\n    print(\"请在主程序中使用完整的数据进行测试\")\n\n\nif __name__ == \"__main__\":\n    main()\n"}]}