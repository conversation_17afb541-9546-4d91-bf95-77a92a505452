{"sourceFile": "src/needle_indicator.py", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1752591423298, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1752591423298, "name": "Commit-0", "content": "\"\"\"\n针指标定义与实现模块\n\n针的专业定义:\n1. 上针(看跌针): 上影线长度 >= 1.5 * ATR, 实体占比 <= 30%, 下影线占比 <= 20%\n2. 下针(看涨针): 下影线长度 >= 1.5 * ATR, 实体占比 <= 30%, 上影线占比 <= 20%\n3. 针的强度 = 影线长度/ATR * (1-实体占比) * (1-反向影线占比)\n\"\"\"\n\nimport pandas as pd\nimport numpy as np\nfrom typing import Dict, Tuple, Optional\n\ntry:\n    from .technical_indicators import TechnicalIndicators\nexcept ImportError:\n    from technical_indicators import TechnicalIndicators\n\n\nclass NeedleIndicator:\n    \"\"\"针指标计算器\"\"\"\n\n    def __init__(\n        self,\n        atr_period: int = 14,\n        min_shadow_atr_ratio: float = 1.5,\n        max_body_ratio: float = 0.3,\n        max_opposite_shadow_ratio: float = 0.2,\n    ):\n        \"\"\"\n        初始化针指标参数\n\n        Args:\n            atr_period: ATR计算周期\n            min_shadow_atr_ratio: 影线长度相对ATR的最小比值\n            max_body_ratio: 实体最大占比\n            max_opposite_shadow_ratio: 反向影线最大占比\n        \"\"\"\n        self.atr_period = atr_period\n        self.min_shadow_atr_ratio = min_shadow_atr_ratio\n        self.max_body_ratio = max_body_ratio\n        self.max_opposite_shadow_ratio = max_opposite_shadow_ratio\n\n    def calculate_kline_components(self, df: pd.DataFrame) -> pd.DataFrame:\n        \"\"\"\n        计算K线各组成部分\n\n        Args:\n            df: 包含OHLC数据的DataFrame\n\n        Returns:\n            包含K线组成部分的DataFrame\n        \"\"\"\n        result = df.copy()\n\n        # 计算ATR\n        result[\"atr\"] = TechnicalIndicators.calculate_atr(df, self.atr_period)\n\n        # 计算实体长度和方向\n        result[\"body_length\"] = abs(result[\"close\"] - result[\"open\"])\n        result[\"is_bullish\"] = result[\"close\"] > result[\"open\"]  # 阳线\n        result[\"is_bearish\"] = result[\"close\"] < result[\"open\"]  # 阴线\n        result[\"is_doji\"] = result[\"close\"] == result[\"open\"]  # 十字星\n\n        # 计算影线长度\n        result[\"upper_shadow\"] = result[\"high\"] - np.maximum(\n            result[\"open\"], result[\"close\"]\n        )\n        result[\"lower_shadow\"] = (\n            np.minimum(result[\"open\"], result[\"close\"]) - result[\"low\"]\n        )\n\n        # 计算K线总长度\n        result[\"total_range\"] = result[\"high\"] - result[\"low\"]\n\n        # 避免除零错误\n        result[\"total_range\"] = np.where(\n            result[\"total_range\"] == 0, 0.0001, result[\"total_range\"]  # 设置最小值\n        )\n\n        # 计算各部分占比\n        result[\"body_ratio\"] = result[\"body_length\"] / result[\"total_range\"]\n        result[\"upper_shadow_ratio\"] = result[\"upper_shadow\"] / result[\"total_range\"]\n        result[\"lower_shadow_ratio\"] = result[\"lower_shadow\"] / result[\"total_range\"]\n\n        # 计算影线相对ATR的比值\n        result[\"upper_shadow_atr_ratio\"] = np.where(\n            result[\"atr\"] > 0, result[\"upper_shadow\"] / result[\"atr\"], 0\n        )\n        result[\"lower_shadow_atr_ratio\"] = np.where(\n            result[\"atr\"] > 0, result[\"lower_shadow\"] / result[\"atr\"], 0\n        )\n\n        return result\n\n    def identify_needles(self, df: pd.DataFrame) -> pd.DataFrame:\n        \"\"\"\n        识别针形态\n\n        Args:\n            df: 包含K线组成部分的DataFrame\n\n        Returns:\n            包含针识别结果的DataFrame\n        \"\"\"\n        result = df.copy()\n\n        # 上针条件 (看跌信号)\n        upper_needle_conditions = (\n            (result[\"upper_shadow_atr_ratio\"] >= self.min_shadow_atr_ratio)\n            & (result[\"body_ratio\"] <= self.max_body_ratio)\n            & (result[\"lower_shadow_ratio\"] <= self.max_opposite_shadow_ratio)\n        )\n\n        # 下针条件 (看涨信号)\n        lower_needle_conditions = (\n            (result[\"lower_shadow_atr_ratio\"] >= self.min_shadow_atr_ratio)\n            & (result[\"body_ratio\"] <= self.max_body_ratio)\n            & (result[\"upper_shadow_ratio\"] <= self.max_opposite_shadow_ratio)\n        )\n\n        result[\"is_upper_needle\"] = upper_needle_conditions\n        result[\"is_lower_needle\"] = lower_needle_conditions\n        result[\"is_needle\"] = upper_needle_conditions | lower_needle_conditions\n\n        # 针的类型\n        result[\"needle_type\"] = np.where(\n            result[\"is_upper_needle\"],\n            \"upper\",\n            np.where(result[\"is_lower_needle\"], \"lower\", \"none\"),\n        )\n\n        return result\n\n    def calculate_needle_strength(self, df: pd.DataFrame) -> pd.DataFrame:\n        \"\"\"\n        计算针的强度\n\n        强度计算公式:\n        - 上针强度 = (上影线/ATR) * (1-实体占比) * (1-下影线占比)\n        - 下针强度 = (下影线/ATR) * (1-实体占比) * (1-上影线占比)\n\n        Args:\n            df: 包含针识别结果的DataFrame\n\n        Returns:\n            包含针强度的DataFrame\n        \"\"\"\n        result = df.copy()\n\n        # 计算上针强度\n        upper_strength = (\n            result[\"upper_shadow_atr_ratio\"]\n            * (1 - result[\"body_ratio\"])\n            * (1 - result[\"lower_shadow_ratio\"])\n        )\n\n        # 计算下针强度\n        lower_strength = (\n            result[\"lower_shadow_atr_ratio\"]\n            * (1 - result[\"body_ratio\"])\n            * (1 - result[\"upper_shadow_ratio\"])\n        )\n\n        # 根据针的类型分配强度\n        result[\"needle_strength\"] = np.where(\n            result[\"is_upper_needle\"],\n            upper_strength,\n            np.where(result[\"is_lower_needle\"], lower_strength, 0),\n        )\n\n        # 计算强度等级\n        result[\"strength_level\"] = pd.cut(\n            result[\"needle_strength\"],\n            bins=[0, 1, 2, 3, float(\"inf\")],\n            labels=[\"weak\", \"medium\", \"strong\", \"very_strong\"],\n            include_lowest=True,\n        )\n\n        return result\n\n    def get_needle_signals(self, df: pd.DataFrame) -> pd.DataFrame:\n        \"\"\"\n        获取针信号\n\n        Args:\n            df: 包含OHLC数据的DataFrame\n\n        Returns:\n            包含完整针分析的DataFrame\n        \"\"\"\n        # 计算K线组成部分\n        result = self.calculate_kline_components(df)\n\n        # 识别针形态\n        result = self.identify_needles(result)\n\n        # 计算针强度\n        result = self.calculate_needle_strength(result)\n\n        return result\n\n    def get_needle_statistics(self, df: pd.DataFrame) -> Dict:\n        \"\"\"\n        获取针的统计信息\n\n        Args:\n            df: 包含针分析的DataFrame\n\n        Returns:\n            针的统计信息字典\n        \"\"\"\n        needle_data = df[df[\"is_needle\"]]\n\n        stats = {\n            \"total_bars\": len(df),\n            \"total_needles\": len(needle_data),\n            \"needle_frequency\": len(needle_data) / len(df) * 100,\n            \"upper_needles\": (df[\"is_upper_needle\"]).sum(),\n            \"lower_needles\": (df[\"is_lower_needle\"]).sum(),\n            \"avg_needle_strength\": (\n                needle_data[\"needle_strength\"].mean() if len(needle_data) > 0 else 0\n            ),\n            \"max_needle_strength\": (\n                needle_data[\"needle_strength\"].max() if len(needle_data) > 0 else 0\n            ),\n            \"strength_distribution\": (\n                needle_data[\"strength_level\"].value_counts().to_dict()\n                if len(needle_data) > 0\n                else {}\n            ),\n        }\n\n        return stats\n\n    def filter_needles_by_strength(\n        self, df: pd.DataFrame, min_strength: float = 1.0\n    ) -> pd.DataFrame:\n        \"\"\"\n        根据强度过滤针\n\n        Args:\n            df: 包含针分析的DataFrame\n            min_strength: 最小强度阈值\n\n        Returns:\n            过滤后的针DataFrame\n        \"\"\"\n        return df[(df[\"is_needle\"]) & (df[\"needle_strength\"] >= min_strength)]\n\n    def get_needle_price_levels(self, df: pd.DataFrame) -> pd.DataFrame:\n        \"\"\"\n        获取针的关键价格位\n\n        Args:\n            df: 包含针分析的DataFrame\n\n        Returns:\n            包含关键价格位的DataFrame\n        \"\"\"\n        result = df.copy()\n\n        # 针的极值点 (用于止损)\n        result[\"needle_extreme\"] = np.where(\n            result[\"is_upper_needle\"],\n            result[\"high\"],  # 上针的最高点\n            np.where(result[\"is_lower_needle\"], result[\"low\"], np.nan),  # 下针的最低点\n        )\n\n        # 针的实体中点 (用于入场参考)\n        result[\"needle_body_mid\"] = (result[\"open\"] + result[\"close\"]) / 2\n\n        # 针的影线中点\n        result[\"needle_shadow_mid\"] = np.where(\n            result[\"is_upper_needle\"],\n            (result[\"high\"] + np.maximum(result[\"open\"], result[\"close\"])) / 2,\n            np.where(\n                result[\"is_lower_needle\"],\n                (result[\"low\"] + np.minimum(result[\"open\"], result[\"close\"])) / 2,\n                np.nan,\n            ),\n        )\n\n        return result\n\n\ndef main():\n    \"\"\"测试针指标功能\"\"\"\n    # 创建测试数据\n    dates = pd.date_range(\"2024-01-01\", periods=200, freq=\"H\")\n    np.random.seed(42)\n\n    # 模拟价格数据，包含一些针形态\n    price = 50000\n    prices = []\n\n    for i in range(200):\n        if i % 50 == 0:  # 每50根K线创建一个针\n            # 创建上针\n            open_price = price\n            close_price = price + np.random.uniform(-100, 100)\n            high_price = max(open_price, close_price) + np.random.uniform(\n                500, 1000\n            )  # 长上影线\n            low_price = min(open_price, close_price) - np.random.uniform(\n                0, 50\n            )  # 短下影线\n        elif i % 75 == 0:  # 创建下针\n            open_price = price\n            close_price = price + np.random.uniform(-100, 100)\n            high_price = max(open_price, close_price) + np.random.uniform(\n                0, 50\n            )  # 短上影线\n            low_price = min(open_price, close_price) - np.random.uniform(\n                500, 1000\n            )  # 长下影线\n        else:\n            # 正常K线\n            change = np.random.normal(0, 0.01)\n            open_price = price\n            close_price = price * (1 + change)\n            high_price = max(open_price, close_price) * (\n                1 + np.random.uniform(0, 0.005)\n            )\n            low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.005))\n\n        prices.append(\n            {\n                \"open\": open_price,\n                \"high\": high_price,\n                \"low\": low_price,\n                \"close\": close_price,\n            }\n        )\n        price = close_price\n\n    # 创建DataFrame\n    df = pd.DataFrame(prices, index=dates)\n    df[\"volume\"] = np.random.uniform(100, 1000, len(df))\n\n    print(\"测试数据创建完成\")\n    print(f\"数据形状: {df.shape}\")\n\n    # 初始化针指标计算器\n    needle_indicator = NeedleIndicator()\n\n    # 计算针指标\n    print(\"\\n计算针指标...\")\n    needle_df = needle_indicator.get_needle_signals(df)\n\n    # 获取统计信息\n    stats = needle_indicator.get_needle_statistics(needle_df)\n    print(f\"\\n针指标统计:\")\n    for key, value in stats.items():\n        print(f\"{key}: {value}\")\n\n    # 显示针的详细信息\n    needles = needle_df[needle_df[\"is_needle\"]]\n    if len(needles) > 0:\n        print(f\"\\n发现的针 (前5个):\")\n        columns = [\n            \"open\",\n            \"high\",\n            \"low\",\n            \"close\",\n            \"needle_type\",\n            \"needle_strength\",\n            \"strength_level\",\n        ]\n        print(needles[columns].head())\n\n        # 获取关键价格位\n        price_levels = needle_indicator.get_needle_price_levels(needle_df)\n        needle_levels = price_levels[price_levels[\"is_needle\"]]\n        print(f\"\\n针的关键价格位:\")\n        level_columns = [\"needle_extreme\", \"needle_body_mid\", \"needle_shadow_mid\"]\n        print(needle_levels[level_columns].head())\n\n\nif __name__ == \"__main__\":\n    main()\n"}]}