{"sourceFile": "src/backtest_engine.py", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1752591763436, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1752591763436, "name": "Commit-0", "content": "\"\"\"\n回测引擎模块\n\n实现完整的回测引擎，包括:\n- 资金管理\n- 手续费计算\n- 滑点模拟\n- 风险控制\n- 绩效统计\n\"\"\"\n\nimport pandas as pd\nimport numpy as np\nfrom typing import Dict, List, Optional, Tuple\nfrom dataclasses import dataclass\nfrom datetime import datetime\nimport warnings\n\nwarnings.filterwarnings(\"ignore\")\n\n\n@dataclass\nclass Trade:\n    \"\"\"交易记录\"\"\"\n\n    entry_time: datetime\n    exit_time: Optional[datetime]\n    signal_type: str  # 'long' or 'short'\n    entry_price: float\n    exit_price: Optional[float]\n    stop_loss: float\n    take_profit: float\n    quantity: float\n    status: (\n        str  # 'open', 'closed_profit', 'closed_loss', 'closed_stop', 'closed_target'\n    )\n    pnl: float = 0.0\n    pnl_pct: float = 0.0\n    commission: float = 0.0\n    slippage: float = 0.0\n\n\nclass BacktestEngine:\n    \"\"\"回测引擎\"\"\"\n\n    def __init__(\n        self,\n        initial_capital: float = 100000,\n        commission_rate: float = 0.001,  # 0.1%\n        slippage_rate: float = 0.0005,  # 0.05%\n        risk_per_trade: float = 0.02,  # 每笔交易风险2%\n        max_positions: int = 1,\n    ):  # 最大持仓数\n        \"\"\"\n        初始化回测引擎\n\n        Args:\n            initial_capital: 初始资金\n            commission_rate: 手续费率\n            slippage_rate: 滑点率\n            risk_per_trade: 每笔交易风险比例\n            max_positions: 最大持仓数\n        \"\"\"\n        self.initial_capital = initial_capital\n        self.commission_rate = commission_rate\n        self.slippage_rate = slippage_rate\n        self.risk_per_trade = risk_per_trade\n        self.max_positions = max_positions\n\n        # 回测状态\n        self.current_capital = initial_capital\n        self.available_capital = initial_capital\n        self.open_trades: List[Trade] = []\n        self.closed_trades: List[Trade] = []\n        self.equity_curve: List[Dict] = []\n\n    def calculate_position_size(\n        self, entry_price: float, stop_loss: float, signal_type: str\n    ) -> float:\n        \"\"\"\n        计算仓位大小\n\n        基于固定风险比例计算仓位\n\n        Args:\n            entry_price: 入场价格\n            stop_loss: 止损价格\n            signal_type: 信号类型\n\n        Returns:\n            仓位大小\n        \"\"\"\n        # 计算每股风险\n        if signal_type == \"long\":\n            risk_per_share = entry_price - stop_loss\n        else:  # short\n            risk_per_share = stop_loss - entry_price\n\n        if risk_per_share <= 0:\n            return 0\n\n        # 计算总风险金额\n        total_risk = self.available_capital * self.risk_per_trade\n\n        # 计算仓位大小\n        position_size = total_risk / risk_per_share\n\n        # 确保不超过可用资金\n        max_position = self.available_capital / entry_price * 0.95  # 留5%缓冲\n        position_size = min(position_size, max_position)\n\n        return max(0, position_size)\n\n    def apply_slippage(self, price: float, signal_type: str, is_entry: bool) -> float:\n        \"\"\"\n        应用滑点\n\n        Args:\n            price: 原始价格\n            signal_type: 信号类型\n            is_entry: 是否为入场\n\n        Returns:\n            调整后价格\n        \"\"\"\n        if signal_type == \"long\":\n            if is_entry:\n                return price * (1 + self.slippage_rate)  # 买入时价格上涨\n            else:\n                return price * (1 - self.slippage_rate)  # 卖出时价格下跌\n        else:  # short\n            if is_entry:\n                return price * (1 - self.slippage_rate)  # 做空时价格下跌\n            else:\n                return price * (1 + self.slippage_rate)  # 平空时价格上涨\n\n    def calculate_commission(self, price: float, quantity: float) -> float:\n        \"\"\"\n        计算手续费\n\n        Args:\n            price: 价格\n            quantity: 数量\n\n        Returns:\n            手续费\n        \"\"\"\n        return price * quantity * self.commission_rate\n\n    def open_position(self, signal_data: Dict, current_price: float) -> bool:\n        \"\"\"\n        开仓\n\n        Args:\n            signal_data: 信号数据\n            current_price: 当前价格\n\n        Returns:\n            是否成功开仓\n        \"\"\"\n        # 检查是否可以开新仓\n        if len(self.open_trades) >= self.max_positions:\n            return False\n\n        signal_type = signal_data[\"signal_type\"]\n        entry_price = signal_data[\"entry_price\"]\n        stop_loss = signal_data[\"stop_loss\"]\n        take_profit = signal_data[\"take_profit\"]\n\n        # 使用当前价格作为实际入场价格\n        actual_entry_price = self.apply_slippage(current_price, signal_type, True)\n\n        # 计算仓位大小\n        quantity = self.calculate_position_size(\n            actual_entry_price, stop_loss, signal_type\n        )\n\n        if quantity <= 0:\n            return False\n\n        # 计算所需资金\n        required_capital = actual_entry_price * quantity\n        commission = self.calculate_commission(actual_entry_price, quantity)\n        total_required = required_capital + commission\n\n        if total_required > self.available_capital:\n            return False\n\n        # 创建交易记录\n        trade = Trade(\n            entry_time=signal_data[\"timestamp\"],\n            exit_time=None,\n            signal_type=signal_type,\n            entry_price=actual_entry_price,\n            exit_price=None,\n            stop_loss=stop_loss,\n            take_profit=take_profit,\n            quantity=quantity,\n            status=\"open\",\n            commission=commission,\n            slippage=abs(actual_entry_price - entry_price),\n        )\n\n        # 更新资金\n        self.available_capital -= total_required\n        self.open_trades.append(trade)\n\n        return True\n\n    def check_exit_conditions(self, trade: Trade, current_data: Dict) -> Optional[str]:\n        \"\"\"\n        检查平仓条件\n\n        Args:\n            trade: 交易记录\n            current_data: 当前K线数据\n\n        Returns:\n            平仓原因 ('stop_loss', 'take_profit', None)\n        \"\"\"\n        high = current_data[\"high\"]\n        low = current_data[\"low\"]\n\n        if trade.signal_type == \"long\":\n            if low <= trade.stop_loss:\n                return \"stop_loss\"\n            elif high >= trade.take_profit:\n                return \"take_profit\"\n        else:  # short\n            if high >= trade.stop_loss:\n                return \"stop_loss\"\n            elif low <= trade.take_profit:\n                return \"take_profit\"\n\n        return None\n\n    def close_position(\n        self, trade: Trade, exit_price: float, exit_reason: str, exit_time: datetime\n    ) -> None:\n        \"\"\"\n        平仓\n\n        Args:\n            trade: 交易记录\n            exit_price: 平仓价格\n            exit_reason: 平仓原因\n            exit_time: 平仓时间\n        \"\"\"\n        # 应用滑点\n        actual_exit_price = self.apply_slippage(exit_price, trade.signal_type, False)\n\n        # 计算手续费\n        exit_commission = self.calculate_commission(actual_exit_price, trade.quantity)\n\n        # 计算盈亏\n        if trade.signal_type == \"long\":\n            pnl = (actual_exit_price - trade.entry_price) * trade.quantity\n        else:  # short\n            pnl = (trade.entry_price - actual_exit_price) * trade.quantity\n\n        # 扣除手续费\n        total_commission = trade.commission + exit_commission\n        net_pnl = pnl - total_commission\n\n        # 计算收益率\n        invested_capital = trade.entry_price * trade.quantity\n        pnl_pct = net_pnl / invested_capital * 100\n\n        # 更新交易记录\n        trade.exit_time = exit_time\n        trade.exit_price = actual_exit_price\n        trade.pnl = net_pnl\n        trade.pnl_pct = pnl_pct\n        trade.commission = total_commission\n        trade.slippage += abs(actual_exit_price - exit_price)\n\n        # 设置状态\n        if exit_reason == \"stop_loss\":\n            trade.status = \"closed_stop\"\n        elif exit_reason == \"take_profit\":\n            trade.status = \"closed_target\"\n        else:\n            trade.status = \"closed_profit\" if net_pnl > 0 else \"closed_loss\"\n\n        # 更新资金\n        returned_capital = actual_exit_price * trade.quantity - exit_commission\n        self.available_capital += returned_capital\n        self.current_capital += net_pnl\n\n        # 移动到已平仓列表\n        self.closed_trades.append(trade)\n\n    def update_equity_curve(self, timestamp: datetime, current_data: Dict) -> None:\n        \"\"\"\n        更新权益曲线\n\n        Args:\n            timestamp: 当前时间\n            current_data: 当前K线数据\n        \"\"\"\n        # 计算未实现盈亏\n        unrealized_pnl = 0\n        for trade in self.open_trades:\n            current_price = current_data[\"close\"]\n            if trade.signal_type == \"long\":\n                unrealized_pnl += (current_price - trade.entry_price) * trade.quantity\n            else:  # short\n                unrealized_pnl += (trade.entry_price - current_price) * trade.quantity\n\n        # 计算总权益\n        total_equity = self.current_capital + unrealized_pnl\n\n        # 记录权益曲线\n        equity_point = {\n            \"timestamp\": timestamp,\n            \"capital\": self.current_capital,\n            \"available_capital\": self.available_capital,\n            \"unrealized_pnl\": unrealized_pnl,\n            \"total_equity\": total_equity,\n            \"open_positions\": len(self.open_trades),\n            \"drawdown\": (total_equity - self.initial_capital)\n            / self.initial_capital\n            * 100,\n        }\n\n        self.equity_curve.append(equity_point)\n\n    def run_backtest(self, df: pd.DataFrame, signals_df: pd.DataFrame) -> Dict:\n        \"\"\"\n        运行回测\n\n        Args:\n            df: 价格数据DataFrame\n            signals_df: 信号数据DataFrame\n\n        Returns:\n            回测结果字典\n        \"\"\"\n        print(\"开始回测...\")\n\n        # 重置状态\n        self.current_capital = self.initial_capital\n        self.available_capital = self.initial_capital\n        self.open_trades = []\n        self.closed_trades = []\n        self.equity_curve = []\n\n        # 获取有效信号\n        valid_signals = signals_df[signals_df[\"valid_signal\"]].copy()\n        signal_dict = valid_signals.to_dict(\"index\")\n\n        # 遍历每根K线\n        for timestamp, row in df.iterrows():\n            current_data = row.to_dict()\n            current_data[\"timestamp\"] = timestamp\n\n            # 检查是否有新信号\n            if timestamp in signal_dict:\n                signal_data = signal_dict[timestamp]\n                signal_data[\"timestamp\"] = timestamp\n                self.open_position(signal_data, row[\"open\"])\n\n            # 检查现有持仓的平仓条件\n            trades_to_close = []\n            for trade in self.open_trades:\n                exit_reason = self.check_exit_conditions(trade, current_data)\n                if exit_reason:\n                    exit_price = (\n                        trade.stop_loss\n                        if exit_reason == \"stop_loss\"\n                        else trade.take_profit\n                    )\n                    trades_to_close.append((trade, exit_price, exit_reason))\n\n            # 执行平仓\n            for trade, exit_price, exit_reason in trades_to_close:\n                self.close_position(trade, exit_price, exit_reason, timestamp)\n                self.open_trades.remove(trade)\n\n            # 更新权益曲线\n            self.update_equity_curve(timestamp, current_data)\n\n        # 强制平仓所有未平仓交易\n        for trade in self.open_trades[:]:\n            final_price = df.iloc[-1][\"close\"]\n            self.close_position(trade, final_price, \"forced_close\", df.index[-1])\n            self.open_trades.remove(trade)\n\n        print(f\"回测完成，共执行 {len(self.closed_trades)} 笔交易\")\n\n        return self.calculate_performance_metrics()\n\n    def calculate_performance_metrics(self) -> Dict:\n        \"\"\"\n        计算绩效指标\n\n        Returns:\n            绩效指标字典\n        \"\"\"\n        if not self.closed_trades:\n            return {\n                \"total_trades\": 0,\n                \"win_rate\": 0,\n                \"total_return\": 0,\n                \"total_pnl\": 0,\n                \"avg_win\": 0,\n                \"avg_loss\": 0,\n                \"profit_factor\": 0,\n                \"max_drawdown\": 0,\n                \"sharpe_ratio\": 0,\n                \"final_capital\": self.current_capital,\n                \"winning_trades\": 0,\n                \"losing_trades\": 0,\n                \"error\": \"没有已完成的交易\",\n            }\n\n        # 基本统计\n        total_trades = len(self.closed_trades)\n        winning_trades = [t for t in self.closed_trades if t.pnl > 0]\n        losing_trades = [t for t in self.closed_trades if t.pnl < 0]\n\n        win_rate = len(winning_trades) / total_trades * 100\n\n        # 盈亏统计\n        total_pnl = sum(t.pnl for t in self.closed_trades)\n        total_return = total_pnl / self.initial_capital * 100\n\n        avg_win = np.mean([t.pnl for t in winning_trades]) if winning_trades else 0\n        avg_loss = np.mean([t.pnl for t in losing_trades]) if losing_trades else 0\n\n        # 最大回撤\n        equity_values = [point[\"total_equity\"] for point in self.equity_curve]\n        peak = self.initial_capital\n        max_drawdown = 0\n\n        for equity in equity_values:\n            if equity > peak:\n                peak = equity\n            drawdown = (peak - equity) / peak * 100\n            max_drawdown = max(max_drawdown, drawdown)\n\n        # 夏普比率 (简化计算)\n        returns = []\n        for i in range(1, len(equity_values)):\n            ret = (equity_values[i] - equity_values[i - 1]) / equity_values[i - 1]\n            returns.append(ret)\n\n        if returns:\n            sharpe_ratio = (\n                np.mean(returns) / np.std(returns) * np.sqrt(252)\n                if np.std(returns) > 0\n                else 0\n            )\n        else:\n            sharpe_ratio = 0\n\n        # 盈亏比\n        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float(\"inf\")\n\n        metrics = {\n            \"total_trades\": total_trades,\n            \"win_rate\": win_rate,\n            \"total_return\": total_return,\n            \"total_pnl\": total_pnl,\n            \"avg_win\": avg_win,\n            \"avg_loss\": avg_loss,\n            \"profit_factor\": profit_factor,\n            \"max_drawdown\": max_drawdown,\n            \"sharpe_ratio\": sharpe_ratio,\n            \"final_capital\": self.current_capital,\n            \"winning_trades\": len(winning_trades),\n            \"losing_trades\": len(losing_trades),\n        }\n\n        return metrics\n\n    def get_trade_analysis(self) -> pd.DataFrame:\n        \"\"\"\n        获取交易分析DataFrame\n\n        Returns:\n            交易分析DataFrame\n        \"\"\"\n        if not self.closed_trades:\n            return pd.DataFrame()\n\n        trade_data = []\n        for trade in self.closed_trades:\n            trade_data.append(\n                {\n                    \"entry_time\": trade.entry_time,\n                    \"exit_time\": trade.exit_time,\n                    \"signal_type\": trade.signal_type,\n                    \"entry_price\": trade.entry_price,\n                    \"exit_price\": trade.exit_price,\n                    \"quantity\": trade.quantity,\n                    \"pnl\": trade.pnl,\n                    \"pnl_pct\": trade.pnl_pct,\n                    \"status\": trade.status,\n                    \"commission\": trade.commission,\n                    \"slippage\": trade.slippage,\n                }\n            )\n\n        return pd.DataFrame(trade_data)\n\n    def get_equity_curve_df(self) -> pd.DataFrame:\n        \"\"\"\n        获取权益曲线DataFrame\n\n        Returns:\n            权益曲线DataFrame\n        \"\"\"\n        return pd.DataFrame(self.equity_curve)\n\n\ndef main():\n    \"\"\"测试回测引擎\"\"\"\n    # 这里应该导入信号生成器和数据\n    # 由于模块依赖，这里只做基本测试\n    print(\"回测引擎模块已创建\")\n    print(\"请在主程序中使用完整的数据和信号进行测试\")\n\n\nif __name__ == \"__main__\":\n    main()\n"}]}