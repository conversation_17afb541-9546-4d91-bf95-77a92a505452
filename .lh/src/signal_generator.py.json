{"sourceFile": "src/signal_generator.py", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1752591733847, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1752591733847, "name": "Commit-0", "content": "\"\"\"\n交易信号生成模块\n\n基于针指标和均线趋势判断生成买入/卖出信号\n实现止损止盈逻辑\n\"\"\"\n\nimport pandas as pd\nimport numpy as np\nfrom typing import Dict, List, Optional, Tuple\n\ntry:\n    from .needle_indicator import NeedleIndicator\n    from .technical_indicators import TechnicalIndicators\nexcept ImportError:\n    from needle_indicator import NeedleIndicator\n    from technical_indicators import TechnicalIndicators\n\n\nclass SignalGenerator:\n    \"\"\"交易信号生成器\"\"\"\n\n    def __init__(\n        self,\n        needle_params: Dict = None,\n        ma_period: int = 20,\n        ma_type: str = \"ema\",\n        min_needle_strength: float = 1.0,\n        risk_reward_ratio: float = 2.0,\n    ):\n        \"\"\"\n        初始化信号生成器\n\n        Args:\n            needle_params: 针指标参数字典\n            ma_period: 移动平均周期\n            ma_type: 移动平均类型\n            min_needle_strength: 最小针强度\n            risk_reward_ratio: 风险收益比\n        \"\"\"\n        self.needle_params = needle_params or {}\n        self.ma_period = ma_period\n        self.ma_type = ma_type\n        self.min_needle_strength = min_needle_strength\n        self.risk_reward_ratio = risk_reward_ratio\n\n        # 初始化针指标计算器\n        self.needle_indicator = NeedleIndicator(**self.needle_params)\n\n    def calculate_trend_filter(self, df: pd.DataFrame) -> pd.DataFrame:\n        \"\"\"\n        计算趋势过滤器\n\n        Args:\n            df: 包含OHLC数据的DataFrame\n\n        Returns:\n            包含趋势信息的DataFrame\n        \"\"\"\n        result = df.copy()\n\n        # 计算移动平均线\n        ma = TechnicalIndicators.calculate_moving_average(\n            df[\"close\"], self.ma_period, self.ma_type\n        )\n        result[\"ma\"] = ma\n\n        # 计算趋势方向\n        result[\"trend\"] = TechnicalIndicators.calculate_trend_direction(\n            df, self.ma_period, self.ma_type\n        )\n\n        # 价格相对于均线的位置\n        result[\"price_vs_ma\"] = df[\"close\"] - ma\n        result[\"above_ma\"] = result[\"price_vs_ma\"] > 0\n        result[\"below_ma\"] = result[\"price_vs_ma\"] < 0\n\n        # 均线斜率\n        result[\"ma_slope\"] = ma.diff()\n        result[\"ma_rising\"] = result[\"ma_slope\"] > 0\n        result[\"ma_falling\"] = result[\"ma_slope\"] < 0\n\n        return result\n\n    def generate_entry_signals(self, df: pd.DataFrame) -> pd.DataFrame:\n        \"\"\"\n        生成入场信号\n\n        交易规则:\n        1. 做多: 下针 + 上涨趋势 (价格在均线上方且均线上涨)\n        2. 做空: 上针 + 下跌趋势 (价格在均线下方且均线下跌)\n        3. 针强度必须大于最小阈值\n\n        Args:\n            df: 包含OHLC数据的DataFrame\n\n        Returns:\n            包含入场信号的DataFrame\n        \"\"\"\n        # 计算针指标\n        result = self.needle_indicator.get_needle_signals(df)\n\n        # 计算趋势过滤器\n        result = self.calculate_trend_filter(result)\n\n        # 过滤强度足够的针\n        strong_needles = result[\"needle_strength\"] >= self.min_needle_strength\n\n        # 做多信号条件\n        long_conditions = (\n            result[\"is_lower_needle\"]  # 下针(看涨)\n            & strong_needles  # 强度足够\n            & result[\"above_ma\"]  # 价格在均线上方\n            & result[\"ma_rising\"]  # 均线上涨\n        )\n\n        # 做空信号条件\n        short_conditions = (\n            result[\"is_upper_needle\"]  # 上针(看跌)\n            & strong_needles  # 强度足够\n            & result[\"below_ma\"]  # 价格在均线下方\n            & result[\"ma_falling\"]  # 均线下跌\n        )\n\n        result[\"long_signal\"] = long_conditions\n        result[\"short_signal\"] = short_conditions\n        result[\"entry_signal\"] = long_conditions | short_conditions\n\n        # 信号类型\n        result[\"signal_type\"] = np.where(\n            long_conditions, \"long\", np.where(short_conditions, \"short\", \"none\")\n        )\n\n        return result\n\n    def calculate_stop_loss_take_profit(self, df: pd.DataFrame) -> pd.DataFrame:\n        \"\"\"\n        计算止损和止盈价格\n\n        止损规则:\n        - 做多: 以下针的最低价作为止损\n        - 做空: 以上针的最高价作为止损\n\n        止盈规则:\n        - 根据风险收益比计算止盈价格\n\n        Args:\n            df: 包含入场信号的DataFrame\n\n        Returns:\n            包含止损止盈的DataFrame\n        \"\"\"\n        result = df.copy()\n\n        # 入场价格 (下一根K线的开盘价)\n        result[\"entry_price\"] = result[\"open\"].shift(-1)\n\n        # 止损价格\n        result[\"stop_loss\"] = np.where(\n            result[\"long_signal\"],\n            result[\"low\"],  # 做多: 针的最低价\n            np.where(\n                result[\"short_signal\"], result[\"high\"], np.nan\n            ),  # 做空: 针的最高价\n        )\n\n        # 计算风险 (入场价与止损价的差距)\n        result[\"risk\"] = np.where(\n            result[\"long_signal\"],\n            result[\"entry_price\"] - result[\"stop_loss\"],  # 做多风险\n            np.where(\n                result[\"short_signal\"],\n                result[\"stop_loss\"] - result[\"entry_price\"],  # 做空风险\n                np.nan,\n            ),\n        )\n\n        # 计算止盈价格\n        result[\"take_profit\"] = np.where(\n            result[\"long_signal\"],\n            result[\"entry_price\"] + result[\"risk\"] * self.risk_reward_ratio,  # 做多止盈\n            np.where(\n                result[\"short_signal\"],\n                result[\"entry_price\"]\n                - result[\"risk\"] * self.risk_reward_ratio,  # 做空止盈\n                np.nan,\n            ),\n        )\n\n        return result\n\n    def generate_complete_signals(self, df: pd.DataFrame) -> pd.DataFrame:\n        \"\"\"\n        生成完整的交易信号\n\n        Args:\n            df: 包含OHLC数据的DataFrame\n\n        Returns:\n            包含完整交易信号的DataFrame\n        \"\"\"\n        # 生成入场信号\n        result = self.generate_entry_signals(df)\n\n        # 计算止损止盈\n        result = self.calculate_stop_loss_take_profit(result)\n\n        # 过滤无效信号 (风险为0或负数的信号)\n        valid_signals = (\n            result[\"entry_signal\"] & (result[\"risk\"] > 0) & result[\"risk\"].notna()\n        )\n\n        result[\"valid_signal\"] = valid_signals\n\n        return result\n\n    def get_signal_summary(self, df: pd.DataFrame) -> Dict:\n        \"\"\"\n        获取信号摘要统计\n\n        Args:\n            df: 包含交易信号的DataFrame\n\n        Returns:\n            信号统计字典\n        \"\"\"\n        signals = df[df[\"valid_signal\"]]\n\n        summary = {\n            \"total_bars\": len(df),\n            \"total_signals\": len(signals),\n            \"signal_frequency\": len(signals) / len(df) * 100 if len(df) > 0 else 0,\n            \"long_signals\": (signals[\"signal_type\"] == \"long\").sum(),\n            \"short_signals\": (signals[\"signal_type\"] == \"short\").sum(),\n            \"avg_risk\": signals[\"risk\"].mean() if len(signals) > 0 else 0,\n            \"avg_reward\": (\n                (signals[\"risk\"] * self.risk_reward_ratio).mean()\n                if len(signals) > 0\n                else 0\n            ),\n            \"avg_needle_strength\": (\n                signals[\"needle_strength\"].mean() if len(signals) > 0 else 0\n            ),\n        }\n\n        return summary\n\n    def get_trade_list(self, df: pd.DataFrame) -> List[Dict]:\n        \"\"\"\n        获取交易列表\n\n        Args:\n            df: 包含交易信号的DataFrame\n\n        Returns:\n            交易字典列表\n        \"\"\"\n        signals = df[df[\"valid_signal\"]].copy()\n\n        trades = []\n        for idx, row in signals.iterrows():\n            trade = {\n                \"timestamp\": idx,\n                \"signal_type\": row[\"signal_type\"],\n                \"entry_price\": row[\"entry_price\"],\n                \"stop_loss\": row[\"stop_loss\"],\n                \"take_profit\": row[\"take_profit\"],\n                \"risk\": row[\"risk\"],\n                \"reward\": row[\"risk\"] * self.risk_reward_ratio,\n                \"risk_reward_ratio\": self.risk_reward_ratio,\n                \"needle_strength\": row[\"needle_strength\"],\n                \"needle_type\": row[\"needle_type\"],\n                \"trend\": row[\"trend\"],\n                \"ma_value\": row[\"ma\"],\n            }\n            trades.append(trade)\n\n        return trades\n\n    def filter_signals_by_time(\n        self, df: pd.DataFrame, min_interval_hours: int = 4\n    ) -> pd.DataFrame:\n        \"\"\"\n        根据时间间隔过滤信号，避免过于频繁的交易\n\n        Args:\n            df: 包含交易信号的DataFrame\n            min_interval_hours: 最小信号间隔(小时)\n\n        Returns:\n            过滤后的DataFrame\n        \"\"\"\n        result = df.copy()\n        signals = result[result[\"valid_signal\"]].copy()\n\n        if len(signals) == 0:\n            return result\n\n        # 按时间排序\n        signals = signals.sort_index()\n\n        # 过滤时间间隔\n        filtered_indices = []\n        last_signal_time = None\n\n        for idx in signals.index:\n            if last_signal_time is None:\n                filtered_indices.append(idx)\n                last_signal_time = idx\n            else:\n                time_diff = (idx - last_signal_time).total_seconds() / 3600\n                if time_diff >= min_interval_hours:\n                    filtered_indices.append(idx)\n                    last_signal_time = idx\n\n        # 更新有效信号标记\n        result[\"valid_signal\"] = False\n        result.loc[filtered_indices, \"valid_signal\"] = True\n        result.loc[filtered_indices, \"entry_signal\"] = True\n\n        return result\n\n\ndef main():\n    \"\"\"测试信号生成功能\"\"\"\n    # 创建测试数据\n    dates = pd.date_range(\"2024-01-01\", periods=500, freq=\"H\")\n    np.random.seed(42)\n\n    # 模拟趋势性价格数据\n    price = 50000\n    trend = 1  # 1: 上涨, -1: 下跌\n    prices = []\n\n    for i in range(500):\n        # 每100根K线改变一次趋势\n        if i % 100 == 0:\n            trend *= -1\n\n        # 基础趋势变化\n        trend_change = trend * np.random.uniform(0.001, 0.003)\n        noise = np.random.normal(0, 0.005)\n\n        open_price = price\n        close_price = price * (1 + trend_change + noise)\n\n        # 偶尔创建针形态\n        if i % 30 == 0 and trend == 1:  # 上涨趋势中的下针\n            high_price = max(open_price, close_price) * (\n                1 + np.random.uniform(0, 0.002)\n            )\n            low_price = min(open_price, close_price) * (\n                1 - np.random.uniform(0.01, 0.02)\n            )\n        elif i % 35 == 0 and trend == -1:  # 下跌趋势中的上针\n            high_price = max(open_price, close_price) * (\n                1 + np.random.uniform(0.01, 0.02)\n            )\n            low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.002))\n        else:  # 正常K线\n            high_price = max(open_price, close_price) * (\n                1 + np.random.uniform(0, 0.005)\n            )\n            low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.005))\n\n        prices.append(\n            {\n                \"open\": open_price,\n                \"high\": high_price,\n                \"low\": low_price,\n                \"close\": close_price,\n            }\n        )\n        price = close_price\n\n    # 创建DataFrame\n    df = pd.DataFrame(prices, index=dates)\n    df[\"volume\"] = np.random.uniform(100, 1000, len(df))\n\n    print(\"测试数据创建完成\")\n    print(f\"数据形状: {df.shape}\")\n    print(f\"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}\")\n\n    # 初始化信号生成器\n    signal_generator = SignalGenerator(\n        ma_period=20, ma_type=\"ema\", min_needle_strength=1.0, risk_reward_ratio=2.0\n    )\n\n    # 生成交易信号\n    print(\"\\n生成交易信号...\")\n    signal_df = signal_generator.generate_complete_signals(df)\n\n    # 过滤信号\n    filtered_df = signal_generator.filter_signals_by_time(\n        signal_df, min_interval_hours=4\n    )\n\n    # 获取信号摘要\n    summary = signal_generator.get_signal_summary(filtered_df)\n    print(f\"\\n信号摘要:\")\n    for key, value in summary.items():\n        print(f\"{key}: {value}\")\n\n    # 获取交易列表\n    trades = signal_generator.get_trade_list(filtered_df)\n    print(f\"\\n交易列表 (前5个):\")\n    for i, trade in enumerate(trades[:5]):\n        print(f\"交易 {i+1}:\")\n        for key, value in trade.items():\n            if isinstance(value, float):\n                print(f\"  {key}: {value:.4f}\")\n            else:\n                print(f\"  {key}: {value}\")\n        print()\n\n\nif __name__ == \"__main__\":\n    main()\n"}]}