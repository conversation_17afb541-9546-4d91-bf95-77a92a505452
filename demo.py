"""
针策略回测系统演示

使用较小的数据集进行快速演示
"""
import sys
import os
import json
from datetime import datetime

# 添加src目录到路径
sys.path.append('src')

def run_demo():
    """运行演示"""
    print("=" * 80)
    print("针策略回测系统演示")
    print("=" * 80)
    
    # 配置参数
    config = {
        'symbol': 'BTCUSDT',
        'interval': '1h',
        'start_date': '2024-06-01',  # 使用较短的时间范围进行演示
        'end_date': '2024-06-15',
        'data_dir': 'demo_data',
        'initial_capital': 100000,
        'commission_rate': 0.001,
        'slippage_rate': 0.0005,
        'risk_per_trade': 0.02,
        'needle_params': {
            'atr_period': 14,
            'min_shadow_atr_ratio': 1.5,
            'max_body_ratio': 0.3,
            'max_opposite_shadow_ratio': 0.2
        },
        'signal_params': {
            'ma_period': 20,
            'ma_type': 'ema',
            'min_needle_strength': 1.0,
            'risk_reward_ratio': 2.0
        }
    }
    
    print(f"配置参数:")
    print(f"  交易对: {config['symbol']}")
    print(f"  时间框架: {config['interval']}")
    print(f"  时间范围: {config['start_date']} 到 {config['end_date']}")
    print(f"  初始资金: ${config['initial_capital']:,}")
    
    try:
        # 1. 下载数据
        print(f"\n{'='*60}")
        print("步骤1: 下载历史数据")
        print("="*60)
        
        from data_downloader import BinanceDataDownloader
        
        downloader = BinanceDataDownloader(config['data_dir'])
        price_data = downloader.download_data(
            symbol=config['symbol'],
            interval=config['interval'],
            start_date=config['start_date'],
            end_date=config['end_date']
        )
        
        if price_data is None or len(price_data) == 0:
            print("⚠️  数据下载失败或数据为空，使用模拟数据进行演示")
            # 使用模拟数据
            import pandas as pd
            import numpy as np
            
            dates = pd.date_range(config['start_date'], config['end_date'], freq='h')
            np.random.seed(42)
            
            price = 70000  # BTC价格
            data = []
            
            for i in range(len(dates)):
                if i % 30 == 0:  # 创建针形态
                    open_price = price
                    close_price = price + np.random.uniform(-500, 500)
                    if i % 60 == 0:  # 上针
                        high_price = max(open_price, close_price) + np.random.uniform(1000, 2000)
                        low_price = min(open_price, close_price) - np.random.uniform(0, 200)
                    else:  # 下针
                        high_price = max(open_price, close_price) + np.random.uniform(0, 200)
                        low_price = min(open_price, close_price) - np.random.uniform(1000, 2000)
                else:
                    change = np.random.normal(0, 0.01)
                    open_price = price
                    close_price = price * (1 + change)
                    high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.01))
                    low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.01))
                
                data.append({
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': np.random.uniform(100, 1000)
                })
                price = close_price
            
            price_data = pd.DataFrame(data, index=dates)
        
        print(f"数据加载完成: {len(price_data)} 条记录")
        print(f"价格范围: ${price_data['close'].min():.2f} - ${price_data['close'].max():.2f}")
        
        # 2. 分析针指标分布
        print(f"\n{'='*60}")
        print("步骤2: 分析针指标分布")
        print("="*60)
        
        from statistical_analysis import StatisticalAnalyzer
        
        analyzer = StatisticalAnalyzer()
        analysis_results = analyzer.analyze_needle_distribution(price_data, [10, 14, 21])
        
        # 生成参数建议
        suggestions = analyzer.suggest_parameter_ranges(analysis_results)
        
        print("参数优化建议:")
        for param, info in suggestions.items():
            if 'error' not in info:
                print(f"  {param}: {info['range']} (默认: {info['default']})")
        
        # 3. 运行回测
        print(f"\n{'='*60}")
        print("步骤3: 运行策略回测")
        print("="*60)
        
        from signal_generator import SignalGenerator
        from backtest_engine import BacktestEngine
        
        # 创建信号生成器
        signal_generator = SignalGenerator(
            needle_params=config['needle_params'],
            ma_period=config['signal_params']['ma_period'],
            ma_type=config['signal_params']['ma_type'],
            min_needle_strength=config['signal_params']['min_needle_strength'],
            risk_reward_ratio=config['signal_params']['risk_reward_ratio']
        )
        
        # 生成交易信号
        signals_df = signal_generator.generate_complete_signals(price_data)
        signals_df = signal_generator.filter_signals_by_time(signals_df, 4)
        
        # 信号统计
        signal_summary = signal_generator.get_signal_summary(signals_df)
        print("信号生成结果:")
        print(f"  总信号数: {signal_summary['total_signals']}")
        print(f"  做多信号: {signal_summary['long_signals']}")
        print(f"  做空信号: {signal_summary['short_signals']}")
        print(f"  信号频率: {signal_summary['signal_frequency']:.2f}%")
        
        # 创建回测引擎
        backtest_engine = BacktestEngine(
            initial_capital=config['initial_capital'],
            commission_rate=config['commission_rate'],
            slippage_rate=config['slippage_rate'],
            risk_per_trade=config['risk_per_trade']
        )
        
        # 运行回测
        metrics = backtest_engine.run_backtest(price_data, signals_df)
        
        # 4. 显示结果
        print(f"\n{'='*60}")
        print("步骤4: 回测结果")
        print("="*60)
        
        print("绩效指标:")
        print(f"  总交易数: {metrics['total_trades']}")
        print(f"  胜率: {metrics['win_rate']:.2f}%")
        print(f"  总收益率: {metrics['total_return']:.2f}%")
        print(f"  最大回撤: {metrics['max_drawdown']:.2f}%")
        print(f"  夏普比率: {metrics['sharpe_ratio']:.3f}")
        print(f"  盈亏比: {metrics['profit_factor']:.2f}")
        print(f"  最终资金: ${metrics['final_capital']:,.2f}")
        
        # 5. 保存结果
        print(f"\n{'='*60}")
        print("步骤5: 保存结果")
        print("="*60)
        
        output_dir = "demo_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存数据和结果
        price_data.to_csv(f"{output_dir}/price_data.csv")
        signals_df.to_csv(f"{output_dir}/signals.csv")
        
        with open(f"{output_dir}/config.json", 'w') as f:
            json.dump(config, f, indent=2)
        
        with open(f"{output_dir}/metrics.json", 'w') as f:
            json.dump(metrics, f, indent=2)
        
        # 获取交易记录
        trades_df = backtest_engine.get_trade_analysis()
        if not trades_df.empty:
            trades_df.to_csv(f"{output_dir}/trades.csv", index=False)
            print(f"  交易记录: {len(trades_df)} 笔交易")
        
        # 获取权益曲线
        equity_df = backtest_engine.get_equity_curve_df()
        if not equity_df.empty:
            equity_df.to_csv(f"{output_dir}/equity_curve.csv", index=False)
        
        print(f"  结果已保存到: {output_dir}/")
        
        # 6. 生成简单的可视化
        print(f"\n{'='*60}")
        print("步骤6: 生成可视化图表")
        print("="*60)
        
        try:
            from visualization import BacktestVisualizer
            
            visualizer = BacktestVisualizer(output_dir)
            
            # 生成权益曲线图
            if not equity_df.empty:
                equity_plot_path = f"{output_dir}/equity_curve.png"
                visualizer.plot_equity_curve(equity_df, metrics, equity_plot_path)
            
            # 生成交易分析图
            if not trades_df.empty:
                trade_plot_path = f"{output_dir}/trade_analysis.png"
                visualizer.plot_trade_analysis(trades_df, trade_plot_path)
            
            # 生成信号分布图
            signal_plot_path = f"{output_dir}/signal_distribution.png"
            visualizer.plot_signal_distribution(signals_df, price_data, signal_plot_path)
            
            print("  图表生成完成")
            
        except Exception as e:
            print(f"  图表生成失败: {e}")
        
        print(f"\n{'='*80}")
        print("演示完成！")
        print("="*80)
        
        print("系统功能验证:")
        print("✓ 数据下载/模拟")
        print("✓ 针指标计算")
        print("✓ 信号生成")
        print("✓ 回测执行")
        print("✓ 结果分析")
        print("✓ 数据保存")
        print("✓ 可视化图表")
        
        print(f"\n查看详细结果请访问: {output_dir}/ 目录")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_demo()
    if success:
        print("\n🎉 针策略回测系统演示成功完成！")
    else:
        print("\n❌ 演示失败，请检查错误信息。")
