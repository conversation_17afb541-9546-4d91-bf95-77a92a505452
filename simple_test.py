"""
简单测试脚本
"""
import sys
import os

print("Python版本:", sys.version)
print("当前目录:", os.getcwd())

try:
    import pandas as pd
    print("✓ pandas导入成功")
except Exception as e:
    print("✗ pandas导入失败:", e)

try:
    import numpy as np
    print("✓ numpy导入成功")
except Exception as e:
    print("✗ numpy导入失败:", e)

try:
    import matplotlib
    matplotlib.use('Agg')
    import matplotlib.pyplot as plt
    print("✓ matplotlib导入成功")
except Exception as e:
    print("✗ matplotlib导入失败:", e)

try:
    import ta
    print("✓ ta导入成功")
except Exception as e:
    print("✗ ta导入失败:", e)

# 测试自定义模块
sys.path.append('src')

try:
    from technical_indicators import TechnicalIndicators
    print("✓ technical_indicators导入成功")
except Exception as e:
    print("✗ technical_indicators导入失败:", e)

try:
    from needle_indicator import NeedleIndicator
    print("✓ needle_indicator导入成功")
except Exception as e:
    print("✗ needle_indicator导入失败:", e)

print("测试完成")
