"""
统计分析模块

统计针指标数值分布，为参数优化提供合理范围
"""

import pandas as pd
import numpy as np
import matplotlib

matplotlib.use("Agg")  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
from scipy import stats
import warnings

warnings.filterwarnings("ignore")

try:
    from .needle_indicator import NeedleIndicator
    from .technical_indicators import TechnicalIndicators
except ImportError:
    from needle_indicator import NeedleIndicator
    from technical_indicators import TechnicalIndicators


class StatisticalAnalyzer:
    """统计分析器"""

    def __init__(self):
        """初始化统计分析器"""
        self.analysis_results = {}

    def analyze_needle_distribution(
        self, df: pd.DataFrame, atr_periods: List[int] = [14, 21, 28]
    ) -> Dict:
        """
        分析针指标分布

        Args:
            df: 包含OHLC数据的DataFrame
            atr_periods: ATR周期列表

        Returns:
            分析结果字典
        """
        print("分析针指标分布...")

        results = {}

        for atr_period in atr_periods:
            print(f"分析ATR周期: {atr_period}")

            # 计算针指标
            needle_indicator = NeedleIndicator(atr_period=atr_period)
            needle_df = needle_indicator.get_needle_signals(df)

            # 基本统计
            total_bars = len(needle_df)
            needle_bars = needle_df[needle_df["is_needle"]]

            if len(needle_bars) == 0:
                results[atr_period] = {"error": "没有发现针形态"}
                continue

            # 针的基本统计
            needle_stats = {
                "total_bars": total_bars,
                "needle_count": len(needle_bars),
                "needle_frequency": len(needle_bars) / total_bars * 100,
                "upper_needles": (needle_bars["is_upper_needle"]).sum(),
                "lower_needles": (needle_bars["is_lower_needle"]).sum(),
            }

            # 影线ATR比值分布
            upper_shadow_ratios = needle_df["upper_shadow_atr_ratio"].dropna()
            lower_shadow_ratios = needle_df["lower_shadow_atr_ratio"].dropna()

            shadow_stats = {
                "upper_shadow_atr_ratio": {
                    "mean": upper_shadow_ratios.mean(),
                    "std": upper_shadow_ratios.std(),
                    "min": upper_shadow_ratios.min(),
                    "max": upper_shadow_ratios.max(),
                    "percentiles": {
                        "25": upper_shadow_ratios.quantile(0.25),
                        "50": upper_shadow_ratios.quantile(0.50),
                        "75": upper_shadow_ratios.quantile(0.75),
                        "90": upper_shadow_ratios.quantile(0.90),
                        "95": upper_shadow_ratios.quantile(0.95),
                    },
                },
                "lower_shadow_atr_ratio": {
                    "mean": lower_shadow_ratios.mean(),
                    "std": lower_shadow_ratios.std(),
                    "min": lower_shadow_ratios.min(),
                    "max": lower_shadow_ratios.max(),
                    "percentiles": {
                        "25": lower_shadow_ratios.quantile(0.25),
                        "50": lower_shadow_ratios.quantile(0.50),
                        "75": lower_shadow_ratios.quantile(0.75),
                        "90": lower_shadow_ratios.quantile(0.90),
                        "95": lower_shadow_ratios.quantile(0.95),
                    },
                },
            }

            # 实体占比分布
            body_ratios = needle_df["body_ratio"].dropna()
            body_stats = {
                "mean": body_ratios.mean(),
                "std": body_ratios.std(),
                "min": body_ratios.min(),
                "max": body_ratios.max(),
                "percentiles": {
                    "25": body_ratios.quantile(0.25),
                    "50": body_ratios.quantile(0.50),
                    "75": body_ratios.quantile(0.75),
                    "90": body_ratios.quantile(0.90),
                    "95": body_ratios.quantile(0.95),
                },
            }

            # 针强度分布
            needle_strengths = needle_bars["needle_strength"].dropna()
            strength_stats = {
                "mean": needle_strengths.mean(),
                "std": needle_strengths.std(),
                "min": needle_strengths.min(),
                "max": needle_strengths.max(),
                "percentiles": {
                    "25": needle_strengths.quantile(0.25),
                    "50": needle_strengths.quantile(0.50),
                    "75": needle_strengths.quantile(0.75),
                    "90": needle_strengths.quantile(0.90),
                    "95": needle_strengths.quantile(0.95),
                },
            }

            results[atr_period] = {
                "needle_stats": needle_stats,
                "shadow_stats": shadow_stats,
                "body_stats": body_stats,
                "strength_stats": strength_stats,
                "raw_data": needle_df,
            }

        self.analysis_results["needle_distribution"] = results
        return results

    def suggest_parameter_ranges(self, analysis_results: Dict = None) -> Dict:
        """
        基于统计分析建议参数范围

        Args:
            analysis_results: 分析结果，如果为None则使用内部结果

        Returns:
            建议的参数范围字典
        """
        if analysis_results is None:
            analysis_results = self.analysis_results.get("needle_distribution", {})

        if not analysis_results:
            return {"error": "没有分析结果，请先运行analyze_needle_distribution"}

        suggestions = {}

        # 分析所有ATR周期的结果
        all_shadow_ratios = []
        all_body_ratios = []
        all_strengths = []

        for atr_period, results in analysis_results.items():
            if "error" in results:
                continue

            # 收集影线ATR比值
            upper_ratios = results["raw_data"]["upper_shadow_atr_ratio"].dropna()
            lower_ratios = results["raw_data"]["lower_shadow_atr_ratio"].dropna()
            all_shadow_ratios.extend(upper_ratios.tolist())
            all_shadow_ratios.extend(lower_ratios.tolist())

            # 收集实体占比
            body_ratios = results["raw_data"]["body_ratio"].dropna()
            all_body_ratios.extend(body_ratios.tolist())

            # 收集针强度
            needle_bars = results["raw_data"][results["raw_data"]["is_needle"]]
            strengths = needle_bars["needle_strength"].dropna()
            all_strengths.extend(strengths.tolist())

        if not all_shadow_ratios:
            return {"error": "没有足够的数据进行分析"}

        # 转换为numpy数组
        shadow_ratios = np.array(all_shadow_ratios)
        body_ratios = np.array(all_body_ratios)
        strengths = np.array(all_strengths)

        # 建议参数范围
        suggestions = {
            "atr_period": {
                "range": (10, 30),
                "type": int,
                "default": 14,
                "description": "ATR计算周期",
            },
            "min_shadow_atr_ratio": {
                "range": (
                    max(0.5, np.percentile(shadow_ratios, 10)),
                    min(5.0, np.percentile(shadow_ratios, 90)),
                ),
                "type": float,
                "default": max(1.0, np.percentile(shadow_ratios, 25)),
                "description": "影线长度相对ATR的最小比值",
            },
            "max_body_ratio": {
                "range": (0.1, min(0.5, np.percentile(body_ratios, 75))),
                "type": float,
                "default": min(0.3, np.percentile(body_ratios, 50)),
                "description": "实体最大占比",
            },
            "max_opposite_shadow_ratio": {
                "range": (0.05, 0.3),
                "type": float,
                "default": 0.2,
                "description": "反向影线最大占比",
            },
            "min_needle_strength": {
                "range": (
                    max(0.5, np.percentile(strengths, 10)),
                    min(3.0, np.percentile(strengths, 75)),
                ),
                "type": float,
                "default": max(1.0, np.percentile(strengths, 25)),
                "description": "最小针强度",
            },
            "ma_period": {
                "range": (10, 50),
                "type": int,
                "default": 20,
                "description": "移动平均周期",
            },
            "risk_reward_ratio": {
                "range": (1.0, 4.0),
                "type": float,
                "default": 2.0,
                "description": "风险收益比",
            },
        }

        self.analysis_results["parameter_suggestions"] = suggestions
        return suggestions

    def plot_distributions(
        self, analysis_results: Dict = None, save_path: Optional[str] = None
    ) -> None:
        """
        绘制分布图

        Args:
            analysis_results: 分析结果
            save_path: 保存路径
        """
        if analysis_results is None:
            analysis_results = self.analysis_results.get("needle_distribution", {})

        if not analysis_results:
            print("没有分析结果可以绘制")
            return

        # 设置图形样式
        plt.style.use("seaborn-v0_8")
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle("针指标分布分析", fontsize=16, fontweight="bold")

        # 收集所有数据
        all_data = {
            "upper_shadow_atr_ratio": [],
            "lower_shadow_atr_ratio": [],
            "body_ratio": [],
            "needle_strength": [],
            "needle_frequency": [],
            "atr_periods": [],
        }

        for atr_period, results in analysis_results.items():
            if "error" in results:
                continue

            df = results["raw_data"]
            needle_bars = df[df["is_needle"]]

            all_data["upper_shadow_atr_ratio"].extend(
                df["upper_shadow_atr_ratio"].dropna().tolist()
            )
            all_data["lower_shadow_atr_ratio"].extend(
                df["lower_shadow_atr_ratio"].dropna().tolist()
            )
            all_data["body_ratio"].extend(df["body_ratio"].dropna().tolist())
            all_data["needle_strength"].extend(
                needle_bars["needle_strength"].dropna().tolist()
            )
            all_data["needle_frequency"].append(
                results["needle_stats"]["needle_frequency"]
            )
            all_data["atr_periods"].append(atr_period)

        # 绘制分布图
        axes[0, 0].hist(
            all_data["upper_shadow_atr_ratio"],
            bins=50,
            alpha=0.7,
            color="red",
            label="上影线/ATR",
        )
        axes[0, 0].hist(
            all_data["lower_shadow_atr_ratio"],
            bins=50,
            alpha=0.7,
            color="green",
            label="下影线/ATR",
        )
        axes[0, 0].set_title("影线ATR比值分布")
        axes[0, 0].set_xlabel("影线/ATR比值")
        axes[0, 0].set_ylabel("频次")
        axes[0, 0].legend()

        axes[0, 1].hist(all_data["body_ratio"], bins=30, alpha=0.7, color="blue")
        axes[0, 1].set_title("实体占比分布")
        axes[0, 1].set_xlabel("实体占比")
        axes[0, 1].set_ylabel("频次")

        axes[0, 2].hist(all_data["needle_strength"], bins=30, alpha=0.7, color="purple")
        axes[0, 2].set_title("针强度分布")
        axes[0, 2].set_xlabel("针强度")
        axes[0, 2].set_ylabel("频次")

        axes[1, 0].bar(
            range(len(all_data["atr_periods"])), all_data["needle_frequency"]
        )
        axes[1, 0].set_title("不同ATR周期的针频率")
        axes[1, 0].set_xlabel("ATR周期")
        axes[1, 0].set_ylabel("针频率 (%)")
        axes[1, 0].set_xticks(range(len(all_data["atr_periods"])))
        axes[1, 0].set_xticklabels(all_data["atr_periods"])

        # 箱线图
        if len(all_data["needle_strength"]) > 0:
            axes[1, 1].boxplot(
                [
                    all_data["upper_shadow_atr_ratio"],
                    all_data["lower_shadow_atr_ratio"],
                ],
                labels=["上影线/ATR", "下影线/ATR"],
            )
            axes[1, 1].set_title("影线ATR比值箱线图")
            axes[1, 1].set_ylabel("比值")

        axes[1, 2].boxplot(
            [all_data["body_ratio"], all_data["needle_strength"]],
            labels=["实体占比", "针强度"],
        )
        axes[1, 2].set_title("实体占比和针强度箱线图")

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")
            print(f"分布图已保存到: {save_path}")

        plt.show()

    def generate_analysis_report(self, analysis_results: Dict = None) -> str:
        """
        生成分析报告

        Args:
            analysis_results: 分析结果

        Returns:
            分析报告字符串
        """
        if analysis_results is None:
            analysis_results = self.analysis_results.get("needle_distribution", {})

        if not analysis_results:
            return "没有分析结果"

        report = []
        report.append("=" * 60)
        report.append("针指标统计分析报告")
        report.append("=" * 60)

        for atr_period, results in analysis_results.items():
            if "error" in results:
                report.append(f"\nATR周期 {atr_period}: {results['error']}")
                continue

            stats = results["needle_stats"]
            shadow_stats = results["shadow_stats"]
            body_stats = results["body_stats"]
            strength_stats = results["strength_stats"]

            report.append(f"\n--- ATR周期: {atr_period} ---")
            report.append(f"总K线数: {stats['total_bars']}")
            report.append(f"针数量: {stats['needle_count']}")
            report.append(f"针频率: {stats['needle_frequency']:.2f}%")
            report.append(f"上针数量: {stats['upper_needles']}")
            report.append(f"下针数量: {stats['lower_needles']}")

            report.append(f"\n影线ATR比值统计:")
            report.append(
                f"  上影线 - 均值: {shadow_stats['upper_shadow_atr_ratio']['mean']:.2f}, "
                f"标准差: {shadow_stats['upper_shadow_atr_ratio']['std']:.2f}"
            )
            report.append(
                f"  下影线 - 均值: {shadow_stats['lower_shadow_atr_ratio']['mean']:.2f}, "
                f"标准差: {shadow_stats['lower_shadow_atr_ratio']['std']:.2f}"
            )

            report.append(f"\n实体占比统计:")
            report.append(
                f"  均值: {body_stats['mean']:.3f}, 标准差: {body_stats['std']:.3f}"
            )
            report.append(f"  75%分位数: {body_stats['percentiles']['75']:.3f}")

            report.append(f"\n针强度统计:")
            report.append(
                f"  均值: {strength_stats['mean']:.2f}, 标准差: {strength_stats['std']:.2f}"
            )
            report.append(f"  25%分位数: {strength_stats['percentiles']['25']:.2f}")
            report.append(f"  75%分位数: {strength_stats['percentiles']['75']:.2f}")

        # 添加参数建议
        suggestions = self.analysis_results.get("parameter_suggestions", {})
        if suggestions and "error" not in suggestions:
            report.append(f"\n{'='*60}")
            report.append("参数优化建议")
            report.append("=" * 60)

            for param, info in suggestions.items():
                if isinstance(info["range"], tuple):
                    range_str = f"({info['range'][0]:.2f}, {info['range'][1]:.2f})"
                else:
                    range_str = str(info["range"])

                report.append(f"{param}:")
                report.append(f"  建议范围: {range_str}")
                report.append(f"  默认值: {info['default']}")
                report.append(f"  说明: {info['description']}")

        return "\n".join(report)


def main():
    """测试统计分析功能"""
    print("统计分析模块已创建")
    print("请在主程序中使用完整的数据进行测试")


if __name__ == "__main__":
    main()
