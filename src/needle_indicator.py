"""
针指标定义与实现模块

针的专业定义:
1. 上针(看跌针): 上影线长度 >= 1.5 * ATR, 实体占比 <= 30%, 下影线占比 <= 20%
2. 下针(看涨针): 下影线长度 >= 1.5 * ATR, 实体占比 <= 30%, 上影线占比 <= 20%
3. 针的强度 = 影线长度/ATR * (1-实体占比) * (1-反向影线占比)
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional

try:
    from .technical_indicators import TechnicalIndicators
except ImportError:
    from technical_indicators import TechnicalIndicators


class NeedleIndicator:
    """针指标计算器"""

    def __init__(
        self,
        atr_period: int = 14,
        min_shadow_atr_ratio: float = 1.5,
        max_body_ratio: float = 0.3,
        max_opposite_shadow_ratio: float = 0.2,
    ):
        """
        初始化针指标参数

        Args:
            atr_period: ATR计算周期
            min_shadow_atr_ratio: 影线长度相对ATR的最小比值
            max_body_ratio: 实体最大占比
            max_opposite_shadow_ratio: 反向影线最大占比
        """
        self.atr_period = atr_period
        self.min_shadow_atr_ratio = min_shadow_atr_ratio
        self.max_body_ratio = max_body_ratio
        self.max_opposite_shadow_ratio = max_opposite_shadow_ratio

    def calculate_kline_components(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算K线各组成部分

        Args:
            df: 包含OHLC数据的DataFrame

        Returns:
            包含K线组成部分的DataFrame
        """
        result = df.copy()

        # 计算ATR
        result["atr"] = TechnicalIndicators.calculate_atr(df, self.atr_period)

        # 计算实体长度和方向
        result["body_length"] = abs(result["close"] - result["open"])
        result["is_bullish"] = result["close"] > result["open"]  # 阳线
        result["is_bearish"] = result["close"] < result["open"]  # 阴线
        result["is_doji"] = result["close"] == result["open"]  # 十字星

        # 计算影线长度
        result["upper_shadow"] = result["high"] - np.maximum(
            result["open"], result["close"]
        )
        result["lower_shadow"] = (
            np.minimum(result["open"], result["close"]) - result["low"]
        )

        # 计算K线总长度
        result["total_range"] = result["high"] - result["low"]

        # 避免除零错误
        result["total_range"] = np.where(
            result["total_range"] == 0, 0.0001, result["total_range"]  # 设置最小值
        )

        # 计算各部分占比
        result["body_ratio"] = result["body_length"] / result["total_range"]
        result["upper_shadow_ratio"] = result["upper_shadow"] / result["total_range"]
        result["lower_shadow_ratio"] = result["lower_shadow"] / result["total_range"]

        # 计算影线相对ATR的比值
        result["upper_shadow_atr_ratio"] = np.where(
            result["atr"] > 0, result["upper_shadow"] / result["atr"], 0
        )
        result["lower_shadow_atr_ratio"] = np.where(
            result["atr"] > 0, result["lower_shadow"] / result["atr"], 0
        )

        return result

    def identify_needles(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        识别针形态

        Args:
            df: 包含K线组成部分的DataFrame

        Returns:
            包含针识别结果的DataFrame
        """
        result = df.copy()

        # 上针条件 (看跌信号)
        upper_needle_conditions = (
            (result["upper_shadow_atr_ratio"] >= self.min_shadow_atr_ratio)
            & (result["body_ratio"] <= self.max_body_ratio)
            & (result["lower_shadow_ratio"] <= self.max_opposite_shadow_ratio)
        )

        # 下针条件 (看涨信号)
        lower_needle_conditions = (
            (result["lower_shadow_atr_ratio"] >= self.min_shadow_atr_ratio)
            & (result["body_ratio"] <= self.max_body_ratio)
            & (result["upper_shadow_ratio"] <= self.max_opposite_shadow_ratio)
        )

        result["is_upper_needle"] = upper_needle_conditions
        result["is_lower_needle"] = lower_needle_conditions
        result["is_needle"] = upper_needle_conditions | lower_needle_conditions

        # 针的类型
        result["needle_type"] = np.where(
            result["is_upper_needle"],
            "upper",
            np.where(result["is_lower_needle"], "lower", "none"),
        )

        return result

    def calculate_needle_strength(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算针的强度

        强度计算公式:
        - 上针强度 = (上影线/ATR) * (1-实体占比) * (1-下影线占比)
        - 下针强度 = (下影线/ATR) * (1-实体占比) * (1-上影线占比)

        Args:
            df: 包含针识别结果的DataFrame

        Returns:
            包含针强度的DataFrame
        """
        result = df.copy()

        # 计算上针强度
        upper_strength = (
            result["upper_shadow_atr_ratio"]
            * (1 - result["body_ratio"])
            * (1 - result["lower_shadow_ratio"])
        )

        # 计算下针强度
        lower_strength = (
            result["lower_shadow_atr_ratio"]
            * (1 - result["body_ratio"])
            * (1 - result["upper_shadow_ratio"])
        )

        # 根据针的类型分配强度
        result["needle_strength"] = np.where(
            result["is_upper_needle"],
            upper_strength,
            np.where(result["is_lower_needle"], lower_strength, 0),
        )

        # 计算强度等级
        result["strength_level"] = pd.cut(
            result["needle_strength"],
            bins=[0, 1, 2, 3, float("inf")],
            labels=["weak", "medium", "strong", "very_strong"],
            include_lowest=True,
        )

        return result

    def get_needle_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        获取针信号

        Args:
            df: 包含OHLC数据的DataFrame

        Returns:
            包含完整针分析的DataFrame
        """
        # 计算K线组成部分
        result = self.calculate_kline_components(df)

        # 识别针形态
        result = self.identify_needles(result)

        # 计算针强度
        result = self.calculate_needle_strength(result)

        return result

    def get_needle_statistics(self, df: pd.DataFrame) -> Dict:
        """
        获取针的统计信息

        Args:
            df: 包含针分析的DataFrame

        Returns:
            针的统计信息字典
        """
        needle_data = df[df["is_needle"]]

        stats = {
            "total_bars": len(df),
            "total_needles": len(needle_data),
            "needle_frequency": len(needle_data) / len(df) * 100,
            "upper_needles": (df["is_upper_needle"]).sum(),
            "lower_needles": (df["is_lower_needle"]).sum(),
            "avg_needle_strength": (
                needle_data["needle_strength"].mean() if len(needle_data) > 0 else 0
            ),
            "max_needle_strength": (
                needle_data["needle_strength"].max() if len(needle_data) > 0 else 0
            ),
            "strength_distribution": (
                needle_data["strength_level"].value_counts().to_dict()
                if len(needle_data) > 0
                else {}
            ),
        }

        return stats

    def filter_needles_by_strength(
        self, df: pd.DataFrame, min_strength: float = 1.0
    ) -> pd.DataFrame:
        """
        根据强度过滤针

        Args:
            df: 包含针分析的DataFrame
            min_strength: 最小强度阈值

        Returns:
            过滤后的针DataFrame
        """
        return df[(df["is_needle"]) & (df["needle_strength"] >= min_strength)]

    def get_needle_price_levels(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        获取针的关键价格位

        Args:
            df: 包含针分析的DataFrame

        Returns:
            包含关键价格位的DataFrame
        """
        result = df.copy()

        # 针的极值点 (用于止损)
        result["needle_extreme"] = np.where(
            result["is_upper_needle"],
            result["high"],  # 上针的最高点
            np.where(result["is_lower_needle"], result["low"], np.nan),  # 下针的最低点
        )

        # 针的实体中点 (用于入场参考)
        result["needle_body_mid"] = (result["open"] + result["close"]) / 2

        # 针的影线中点
        result["needle_shadow_mid"] = np.where(
            result["is_upper_needle"],
            (result["high"] + np.maximum(result["open"], result["close"])) / 2,
            np.where(
                result["is_lower_needle"],
                (result["low"] + np.minimum(result["open"], result["close"])) / 2,
                np.nan,
            ),
        )

        return result


def main():
    """测试针指标功能"""
    # 创建测试数据
    dates = pd.date_range("2024-01-01", periods=200, freq="H")
    np.random.seed(42)

    # 模拟价格数据，包含一些针形态
    price = 50000
    prices = []

    for i in range(200):
        if i % 50 == 0:  # 每50根K线创建一个针
            # 创建上针
            open_price = price
            close_price = price + np.random.uniform(-100, 100)
            high_price = max(open_price, close_price) + np.random.uniform(
                500, 1000
            )  # 长上影线
            low_price = min(open_price, close_price) - np.random.uniform(
                0, 50
            )  # 短下影线
        elif i % 75 == 0:  # 创建下针
            open_price = price
            close_price = price + np.random.uniform(-100, 100)
            high_price = max(open_price, close_price) + np.random.uniform(
                0, 50
            )  # 短上影线
            low_price = min(open_price, close_price) - np.random.uniform(
                500, 1000
            )  # 长下影线
        else:
            # 正常K线
            change = np.random.normal(0, 0.01)
            open_price = price
            close_price = price * (1 + change)
            high_price = max(open_price, close_price) * (
                1 + np.random.uniform(0, 0.005)
            )
            low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.005))

        prices.append(
            {
                "open": open_price,
                "high": high_price,
                "low": low_price,
                "close": close_price,
            }
        )
        price = close_price

    # 创建DataFrame
    df = pd.DataFrame(prices, index=dates)
    df["volume"] = np.random.uniform(100, 1000, len(df))

    print("测试数据创建完成")
    print(f"数据形状: {df.shape}")

    # 初始化针指标计算器
    needle_indicator = NeedleIndicator()

    # 计算针指标
    print("\n计算针指标...")
    needle_df = needle_indicator.get_needle_signals(df)

    # 获取统计信息
    stats = needle_indicator.get_needle_statistics(needle_df)
    print(f"\n针指标统计:")
    for key, value in stats.items():
        print(f"{key}: {value}")

    # 显示针的详细信息
    needles = needle_df[needle_df["is_needle"]]
    if len(needles) > 0:
        print(f"\n发现的针 (前5个):")
        columns = [
            "open",
            "high",
            "low",
            "close",
            "needle_type",
            "needle_strength",
            "strength_level",
        ]
        print(needles[columns].head())

        # 获取关键价格位
        price_levels = needle_indicator.get_needle_price_levels(needle_df)
        needle_levels = price_levels[price_levels["is_needle"]]
        print(f"\n针的关键价格位:")
        level_columns = ["needle_extreme", "needle_body_mid", "needle_shadow_mid"]
        print(needle_levels[level_columns].head())


if __name__ == "__main__":
    main()
