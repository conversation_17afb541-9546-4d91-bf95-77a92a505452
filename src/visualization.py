"""
可视化与报告模块

生成收益曲线、回撤图、信号分布图等，输出详细的回测报告
"""

import pandas as pd
import numpy as np
import matplotlib

matplotlib.use("Agg")  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import plotly.graph_objects as go
import plotly.subplots as sp
from plotly.offline import plot
from typing import Dict, List, Optional, Tuple
import os
from datetime import datetime
import warnings

warnings.filterwarnings("ignore")


class BacktestVisualizer:
    """回测可视化器"""

    def __init__(self, output_dir: str = "output"):
        """
        初始化可视化器

        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

        # 设置中文字体
        plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
        plt.rcParams["axes.unicode_minus"] = False

    def plot_equity_curve(
        self, equity_df: pd.DataFrame, metrics: Dict, save_path: Optional[str] = None
    ) -> None:
        """
        绘制权益曲线

        Args:
            equity_df: 权益曲线DataFrame
            metrics: 绩效指标
            save_path: 保存路径
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

        # 权益曲线
        ax1.plot(
            equity_df["timestamp"],
            equity_df["total_equity"],
            linewidth=2,
            color="blue",
            label="总权益",
        )
        ax1.plot(
            equity_df["timestamp"],
            equity_df["capital"],
            linewidth=1,
            color="green",
            alpha=0.7,
            label="已实现资金",
        )

        ax1.set_title(
            f'权益曲线 - 总收益: {metrics["total_return"]:.2f}%, '
            f'最大回撤: {metrics["max_drawdown"]:.2f}%',
            fontsize=14,
        )
        ax1.set_ylabel("权益 ($)")
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 回撤图
        peak = equity_df["total_equity"].expanding().max()
        drawdown = (equity_df["total_equity"] - peak) / peak * 100

        ax2.fill_between(
            equity_df["timestamp"], drawdown, 0, color="red", alpha=0.3, label="回撤"
        )
        ax2.plot(equity_df["timestamp"], drawdown, color="red", linewidth=1)

        ax2.set_title("回撤曲线")
        ax2.set_xlabel("时间")
        ax2.set_ylabel("回撤 (%)")
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 格式化x轴
        for ax in [ax1, ax2]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d"))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")
            print(f"权益曲线图已保存到: {save_path}")

        plt.show()

    def plot_trade_analysis(
        self, trades_df: pd.DataFrame, save_path: Optional[str] = None
    ) -> None:
        """
        绘制交易分析图

        Args:
            trades_df: 交易DataFrame
            save_path: 保存路径
        """
        if trades_df.empty:
            print("没有交易数据可以绘制")
            return

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # 盈亏分布
        axes[0, 0].hist(
            trades_df["pnl"],
            bins=30,
            alpha=0.7,
            color=["red" if x < 0 else "green" for x in trades_df["pnl"]],
        )
        axes[0, 0].set_title("盈亏分布")
        axes[0, 0].set_xlabel("盈亏 ($)")
        axes[0, 0].set_ylabel("交易次数")
        axes[0, 0].axvline(x=0, color="black", linestyle="--", alpha=0.5)

        # 盈亏百分比分布
        axes[0, 1].hist(
            trades_df["pnl_pct"],
            bins=30,
            alpha=0.7,
            color=["red" if x < 0 else "green" for x in trades_df["pnl_pct"]],
        )
        axes[0, 1].set_title("盈亏百分比分布")
        axes[0, 1].set_xlabel("盈亏百分比 (%)")
        axes[0, 1].set_ylabel("交易次数")
        axes[0, 1].axvline(x=0, color="black", linestyle="--", alpha=0.5)

        # 交易类型分布
        signal_counts = trades_df["signal_type"].value_counts()
        axes[1, 0].pie(
            signal_counts.values, labels=signal_counts.index, autopct="%1.1f%%"
        )
        axes[1, 0].set_title("交易类型分布")

        # 交易状态分布
        status_counts = trades_df["status"].value_counts()
        axes[1, 1].bar(status_counts.index, status_counts.values)
        axes[1, 1].set_title("交易状态分布")
        axes[1, 1].set_xlabel("状态")
        axes[1, 1].set_ylabel("交易次数")
        plt.setp(axes[1, 1].xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")
            print(f"交易分析图已保存到: {save_path}")

        plt.show()

    def plot_signal_distribution(
        self,
        signals_df: pd.DataFrame,
        price_df: pd.DataFrame,
        save_path: Optional[str] = None,
    ) -> None:
        """
        绘制信号分布图

        Args:
            signals_df: 信号DataFrame
            price_df: 价格DataFrame
            save_path: 保存路径
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

        # 价格和信号
        ax1.plot(
            price_df.index, price_df["close"], linewidth=1, color="black", alpha=0.7
        )

        # 标记信号点
        long_signals = signals_df[signals_df["signal_type"] == "long"]
        short_signals = signals_df[signals_df["signal_type"] == "short"]

        if not long_signals.empty:
            ax1.scatter(
                long_signals.index,
                long_signals["close"],
                color="green",
                marker="^",
                s=50,
                label="做多信号",
                zorder=5,
            )

        if not short_signals.empty:
            ax1.scatter(
                short_signals.index,
                short_signals["close"],
                color="red",
                marker="v",
                s=50,
                label="做空信号",
                zorder=5,
            )

        ax1.set_title("价格走势与交易信号")
        ax1.set_ylabel("价格")
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 针强度分布
        needle_data = signals_df[signals_df["is_needle"]]
        if not needle_data.empty:
            ax2.scatter(
                needle_data.index,
                needle_data["needle_strength"],
                c=[
                    "red" if x == "upper" else "green"
                    for x in needle_data["needle_type"]
                ],
                alpha=0.6,
                s=30,
            )
            ax2.set_title("针强度分布")
            ax2.set_xlabel("时间")
            ax2.set_ylabel("针强度")
            ax2.grid(True, alpha=0.3)

        # 格式化x轴
        for ax in [ax1, ax2]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d"))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")
            print(f"信号分布图已保存到: {save_path}")

        plt.show()

    def create_interactive_chart(
        self,
        price_df: pd.DataFrame,
        signals_df: pd.DataFrame,
        trades_df: pd.DataFrame,
        save_path: Optional[str] = None,
    ) -> None:
        """
        创建交互式图表

        Args:
            price_df: 价格DataFrame
            signals_df: 信号DataFrame
            trades_df: 交易DataFrame
            save_path: 保存路径
        """
        # 创建子图
        fig = sp.make_subplots(
            rows=3,
            cols=1,
            subplot_titles=("价格走势与交易信号", "针强度", "累计盈亏"),
            vertical_spacing=0.08,
            row_heights=[0.5, 0.25, 0.25],
        )

        # 价格走势
        fig.add_trace(
            go.Candlestick(
                x=price_df.index,
                open=price_df["open"],
                high=price_df["high"],
                low=price_df["low"],
                close=price_df["close"],
                name="价格",
            ),
            row=1,
            col=1,
        )

        # 交易信号
        long_signals = signals_df[signals_df["signal_type"] == "long"]
        short_signals = signals_df[signals_df["signal_type"] == "short"]

        if not long_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=long_signals.index,
                    y=long_signals["close"],
                    mode="markers",
                    marker=dict(symbol="triangle-up", size=10, color="green"),
                    name="做多信号",
                ),
                row=1,
                col=1,
            )

        if not short_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=short_signals.index,
                    y=short_signals["close"],
                    mode="markers",
                    marker=dict(symbol="triangle-down", size=10, color="red"),
                    name="做空信号",
                ),
                row=1,
                col=1,
            )

        # 针强度
        needle_data = signals_df[signals_df["is_needle"]]
        if not needle_data.empty:
            colors = [
                "red" if x == "upper" else "green" for x in needle_data["needle_type"]
            ]
            fig.add_trace(
                go.Scatter(
                    x=needle_data.index,
                    y=needle_data["needle_strength"],
                    mode="markers",
                    marker=dict(color=colors, size=6),
                    name="针强度",
                ),
                row=2,
                col=1,
            )

        # 累计盈亏
        if not trades_df.empty:
            cumulative_pnl = trades_df["pnl"].cumsum()
            fig.add_trace(
                go.Scatter(
                    x=trades_df["exit_time"],
                    y=cumulative_pnl,
                    mode="lines",
                    line=dict(color="blue", width=2),
                    name="累计盈亏",
                ),
                row=3,
                col=1,
            )

        # 更新布局
        fig.update_layout(
            title="针策略回测分析",
            height=800,
            showlegend=True,
            xaxis_rangeslider_visible=False,
        )

        # 保存或显示
        if save_path:
            plot(fig, filename=save_path, auto_open=False)
            print(f"交互式图表已保存到: {save_path}")
        else:
            fig.show()

    def generate_performance_report(
        self,
        metrics: Dict,
        trades_df: pd.DataFrame,
        parameters: Dict,
        save_path: Optional[str] = None,
    ) -> str:
        """
        生成绩效报告

        Args:
            metrics: 绩效指标
            trades_df: 交易DataFrame
            parameters: 策略参数
            save_path: 保存路径

        Returns:
            报告字符串
        """
        report = []
        report.append("=" * 80)
        report.append("针策略回测绩效报告")
        report.append("=" * 80)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # 策略参数
        report.append("策略参数:")
        report.append("-" * 40)
        for key, value in parameters.items():
            report.append(f"{key}: {value}")
        report.append("")

        # 基本绩效指标
        report.append("基本绩效指标:")
        report.append("-" * 40)
        report.append(f"总交易次数: {metrics.get('total_trades', 0)}")
        report.append(f"胜率: {metrics.get('win_rate', 0):.2f}%")
        report.append(f"总收益率: {metrics.get('total_return', 0):.2f}%")
        report.append(f"总盈亏: ${metrics.get('total_pnl', 0):,.2f}")
        report.append(f"最大回撤: {metrics.get('max_drawdown', 0):.2f}%")
        report.append(f"夏普比率: {metrics.get('sharpe_ratio', 0):.3f}")
        report.append(f"盈亏比: {metrics.get('profit_factor', 0):.2f}")
        report.append("")

        # 交易统计
        if not trades_df.empty:
            winning_trades = trades_df[trades_df["pnl"] > 0]
            losing_trades = trades_df[trades_df["pnl"] < 0]

            report.append("交易统计:")
            report.append("-" * 40)
            report.append(f"盈利交易: {len(winning_trades)}")
            report.append(f"亏损交易: {len(losing_trades)}")
            report.append(
                f"平均盈利: ${winning_trades['pnl'].mean():.2f}"
                if len(winning_trades) > 0
                else "平均盈利: $0.00"
            )
            report.append(
                f"平均亏损: ${losing_trades['pnl'].mean():.2f}"
                if len(losing_trades) > 0
                else "平均亏损: $0.00"
            )
            report.append(f"最大单笔盈利: ${trades_df['pnl'].max():.2f}")
            report.append(f"最大单笔亏损: ${trades_df['pnl'].min():.2f}")
            report.append("")

            # 交易类型分析
            signal_stats = trades_df["signal_type"].value_counts()
            report.append("交易类型分析:")
            report.append("-" * 40)
            for signal_type, count in signal_stats.items():
                type_trades = trades_df[trades_df["signal_type"] == signal_type]
                type_pnl = type_trades["pnl"].sum()
                type_win_rate = (type_trades["pnl"] > 0).mean() * 100
                report.append(
                    f"{signal_type}: {count}笔, 盈亏: ${type_pnl:.2f}, 胜率: {type_win_rate:.1f}%"
                )

        report_text = "\n".join(report)

        if save_path:
            with open(save_path, "w", encoding="utf-8") as f:
                f.write(report_text)
            print(f"绩效报告已保存到: {save_path}")

        return report_text


def main():
    """测试可视化功能"""
    print("可视化模块已创建")
    print("请在主程序中使用完整的数据进行测试")


if __name__ == "__main__":
    main()
