"""
回测引擎模块

实现完整的回测引擎，包括:
- 资金管理
- 手续费计算
- 滑点模拟
- 风险控制
- 绩效统计
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import warnings

warnings.filterwarnings("ignore")


@dataclass
class Trade:
    """交易记录"""

    entry_time: datetime
    exit_time: Optional[datetime]
    signal_type: str  # 'long' or 'short'
    entry_price: float
    exit_price: Optional[float]
    stop_loss: float
    take_profit: float
    quantity: float
    status: (
        str  # 'open', 'closed_profit', 'closed_loss', 'closed_stop', 'closed_target'
    )
    pnl: float = 0.0
    pnl_pct: float = 0.0
    commission: float = 0.0
    slippage: float = 0.0


class BacktestEngine:
    """回测引擎"""

    def __init__(
        self,
        initial_capital: float = 100000,
        commission_rate: float = 0.001,  # 0.1%
        slippage_rate: float = 0.0005,  # 0.05%
        risk_per_trade: float = 0.02,  # 每笔交易风险2%
        max_positions: int = 1,
    ):  # 最大持仓数
        """
        初始化回测引擎

        Args:
            initial_capital: 初始资金
            commission_rate: 手续费率
            slippage_rate: 滑点率
            risk_per_trade: 每笔交易风险比例
            max_positions: 最大持仓数
        """
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        self.risk_per_trade = risk_per_trade
        self.max_positions = max_positions

        # 回测状态
        self.current_capital = initial_capital
        self.available_capital = initial_capital
        self.open_trades: List[Trade] = []
        self.closed_trades: List[Trade] = []
        self.equity_curve: List[Dict] = []

    def calculate_position_size(
        self, entry_price: float, stop_loss: float, signal_type: str
    ) -> float:
        """
        计算仓位大小

        基于固定风险比例计算仓位

        Args:
            entry_price: 入场价格
            stop_loss: 止损价格
            signal_type: 信号类型

        Returns:
            仓位大小
        """
        # 计算每股风险
        if signal_type == "long":
            risk_per_share = entry_price - stop_loss
        else:  # short
            risk_per_share = stop_loss - entry_price

        if risk_per_share <= 0:
            return 0

        # 计算总风险金额
        total_risk = self.available_capital * self.risk_per_trade

        # 计算仓位大小
        position_size = total_risk / risk_per_share

        # 确保不超过可用资金
        max_position = self.available_capital / entry_price * 0.95  # 留5%缓冲
        position_size = min(position_size, max_position)

        return max(0, position_size)

    def apply_slippage(self, price: float, signal_type: str, is_entry: bool) -> float:
        """
        应用滑点

        Args:
            price: 原始价格
            signal_type: 信号类型
            is_entry: 是否为入场

        Returns:
            调整后价格
        """
        if signal_type == "long":
            if is_entry:
                return price * (1 + self.slippage_rate)  # 买入时价格上涨
            else:
                return price * (1 - self.slippage_rate)  # 卖出时价格下跌
        else:  # short
            if is_entry:
                return price * (1 - self.slippage_rate)  # 做空时价格下跌
            else:
                return price * (1 + self.slippage_rate)  # 平空时价格上涨

    def calculate_commission(self, price: float, quantity: float) -> float:
        """
        计算手续费

        Args:
            price: 价格
            quantity: 数量

        Returns:
            手续费
        """
        return price * quantity * self.commission_rate

    def open_position(self, signal_data: Dict, current_price: float) -> bool:
        """
        开仓

        Args:
            signal_data: 信号数据
            current_price: 当前价格

        Returns:
            是否成功开仓
        """
        # 检查是否可以开新仓
        if len(self.open_trades) >= self.max_positions:
            return False

        signal_type = signal_data["signal_type"]
        entry_price = signal_data["entry_price"]
        stop_loss = signal_data["stop_loss"]
        take_profit = signal_data["take_profit"]

        # 使用当前价格作为实际入场价格
        actual_entry_price = self.apply_slippage(current_price, signal_type, True)

        # 计算仓位大小
        quantity = self.calculate_position_size(
            actual_entry_price, stop_loss, signal_type
        )

        if quantity <= 0:
            return False

        # 计算所需资金
        required_capital = actual_entry_price * quantity
        commission = self.calculate_commission(actual_entry_price, quantity)
        total_required = required_capital + commission

        if total_required > self.available_capital:
            return False

        # 创建交易记录
        trade = Trade(
            entry_time=signal_data["timestamp"],
            exit_time=None,
            signal_type=signal_type,
            entry_price=actual_entry_price,
            exit_price=None,
            stop_loss=stop_loss,
            take_profit=take_profit,
            quantity=quantity,
            status="open",
            commission=commission,
            slippage=abs(actual_entry_price - entry_price),
        )

        # 更新资金
        self.available_capital -= total_required
        self.open_trades.append(trade)

        return True

    def check_exit_conditions(self, trade: Trade, current_data: Dict) -> Optional[str]:
        """
        检查平仓条件

        Args:
            trade: 交易记录
            current_data: 当前K线数据

        Returns:
            平仓原因 ('stop_loss', 'take_profit', None)
        """
        high = current_data["high"]
        low = current_data["low"]

        if trade.signal_type == "long":
            if low <= trade.stop_loss:
                return "stop_loss"
            elif high >= trade.take_profit:
                return "take_profit"
        else:  # short
            if high >= trade.stop_loss:
                return "stop_loss"
            elif low <= trade.take_profit:
                return "take_profit"

        return None

    def close_position(
        self, trade: Trade, exit_price: float, exit_reason: str, exit_time: datetime
    ) -> None:
        """
        平仓

        Args:
            trade: 交易记录
            exit_price: 平仓价格
            exit_reason: 平仓原因
            exit_time: 平仓时间
        """
        # 应用滑点
        actual_exit_price = self.apply_slippage(exit_price, trade.signal_type, False)

        # 计算手续费
        exit_commission = self.calculate_commission(actual_exit_price, trade.quantity)

        # 计算盈亏
        if trade.signal_type == "long":
            pnl = (actual_exit_price - trade.entry_price) * trade.quantity
        else:  # short
            pnl = (trade.entry_price - actual_exit_price) * trade.quantity

        # 扣除手续费
        total_commission = trade.commission + exit_commission
        net_pnl = pnl - total_commission

        # 计算收益率
        invested_capital = trade.entry_price * trade.quantity
        pnl_pct = net_pnl / invested_capital * 100

        # 更新交易记录
        trade.exit_time = exit_time
        trade.exit_price = actual_exit_price
        trade.pnl = net_pnl
        trade.pnl_pct = pnl_pct
        trade.commission = total_commission
        trade.slippage += abs(actual_exit_price - exit_price)

        # 设置状态
        if exit_reason == "stop_loss":
            trade.status = "closed_stop"
        elif exit_reason == "take_profit":
            trade.status = "closed_target"
        else:
            trade.status = "closed_profit" if net_pnl > 0 else "closed_loss"

        # 更新资金
        returned_capital = actual_exit_price * trade.quantity - exit_commission
        self.available_capital += returned_capital
        self.current_capital += net_pnl

        # 移动到已平仓列表
        self.closed_trades.append(trade)

    def update_equity_curve(self, timestamp: datetime, current_data: Dict) -> None:
        """
        更新权益曲线

        Args:
            timestamp: 当前时间
            current_data: 当前K线数据
        """
        # 计算未实现盈亏
        unrealized_pnl = 0
        for trade in self.open_trades:
            current_price = current_data["close"]
            if trade.signal_type == "long":
                unrealized_pnl += (current_price - trade.entry_price) * trade.quantity
            else:  # short
                unrealized_pnl += (trade.entry_price - current_price) * trade.quantity

        # 计算总权益
        total_equity = self.current_capital + unrealized_pnl

        # 记录权益曲线
        equity_point = {
            "timestamp": timestamp,
            "capital": self.current_capital,
            "available_capital": self.available_capital,
            "unrealized_pnl": unrealized_pnl,
            "total_equity": total_equity,
            "open_positions": len(self.open_trades),
            "drawdown": (total_equity - self.initial_capital)
            / self.initial_capital
            * 100,
        }

        self.equity_curve.append(equity_point)

    def run_backtest(self, df: pd.DataFrame, signals_df: pd.DataFrame) -> Dict:
        """
        运行回测

        Args:
            df: 价格数据DataFrame
            signals_df: 信号数据DataFrame

        Returns:
            回测结果字典
        """
        print("开始回测...")

        # 重置状态
        self.current_capital = self.initial_capital
        self.available_capital = self.initial_capital
        self.open_trades = []
        self.closed_trades = []
        self.equity_curve = []

        # 获取有效信号
        valid_signals = signals_df[signals_df["valid_signal"]].copy()
        signal_dict = valid_signals.to_dict("index")

        # 遍历每根K线
        for timestamp, row in df.iterrows():
            current_data = row.to_dict()
            current_data["timestamp"] = timestamp

            # 检查是否有新信号
            if timestamp in signal_dict:
                signal_data = signal_dict[timestamp]
                signal_data["timestamp"] = timestamp
                self.open_position(signal_data, row["open"])

            # 检查现有持仓的平仓条件
            trades_to_close = []
            for trade in self.open_trades:
                exit_reason = self.check_exit_conditions(trade, current_data)
                if exit_reason:
                    exit_price = (
                        trade.stop_loss
                        if exit_reason == "stop_loss"
                        else trade.take_profit
                    )
                    trades_to_close.append((trade, exit_price, exit_reason))

            # 执行平仓
            for trade, exit_price, exit_reason in trades_to_close:
                self.close_position(trade, exit_price, exit_reason, timestamp)
                self.open_trades.remove(trade)

            # 更新权益曲线
            self.update_equity_curve(timestamp, current_data)

        # 强制平仓所有未平仓交易
        for trade in self.open_trades[:]:
            final_price = df.iloc[-1]["close"]
            self.close_position(trade, final_price, "forced_close", df.index[-1])
            self.open_trades.remove(trade)

        print(f"回测完成，共执行 {len(self.closed_trades)} 笔交易")

        return self.calculate_performance_metrics()

    def calculate_performance_metrics(self) -> Dict:
        """
        计算绩效指标

        Returns:
            绩效指标字典
        """
        if not self.closed_trades:
            return {
                "total_trades": 0,
                "win_rate": 0,
                "total_return": 0,
                "total_pnl": 0,
                "avg_win": 0,
                "avg_loss": 0,
                "profit_factor": 0,
                "max_drawdown": 0,
                "sharpe_ratio": 0,
                "final_capital": self.current_capital,
                "winning_trades": 0,
                "losing_trades": 0,
                "error": "没有已完成的交易",
            }

        # 基本统计
        total_trades = len(self.closed_trades)
        winning_trades = [t for t in self.closed_trades if t.pnl > 0]
        losing_trades = [t for t in self.closed_trades if t.pnl < 0]

        win_rate = len(winning_trades) / total_trades * 100

        # 盈亏统计
        total_pnl = sum(t.pnl for t in self.closed_trades)
        total_return = total_pnl / self.initial_capital * 100

        avg_win = np.mean([t.pnl for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t.pnl for t in losing_trades]) if losing_trades else 0

        # 最大回撤
        equity_values = [point["total_equity"] for point in self.equity_curve]
        peak = self.initial_capital
        max_drawdown = 0

        for equity in equity_values:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak * 100
            max_drawdown = max(max_drawdown, drawdown)

        # 夏普比率 (简化计算)
        returns = []
        for i in range(1, len(equity_values)):
            ret = (equity_values[i] - equity_values[i - 1]) / equity_values[i - 1]
            returns.append(ret)

        if returns:
            sharpe_ratio = (
                np.mean(returns) / np.std(returns) * np.sqrt(252)
                if np.std(returns) > 0
                else 0
            )
        else:
            sharpe_ratio = 0

        # 盈亏比
        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float("inf")

        metrics = {
            "total_trades": total_trades,
            "win_rate": win_rate,
            "total_return": total_return,
            "total_pnl": total_pnl,
            "avg_win": avg_win,
            "avg_loss": avg_loss,
            "profit_factor": profit_factor,
            "max_drawdown": max_drawdown,
            "sharpe_ratio": sharpe_ratio,
            "final_capital": self.current_capital,
            "winning_trades": len(winning_trades),
            "losing_trades": len(losing_trades),
        }

        return metrics

    def get_trade_analysis(self) -> pd.DataFrame:
        """
        获取交易分析DataFrame

        Returns:
            交易分析DataFrame
        """
        if not self.closed_trades:
            return pd.DataFrame()

        trade_data = []
        for trade in self.closed_trades:
            trade_data.append(
                {
                    "entry_time": trade.entry_time,
                    "exit_time": trade.exit_time,
                    "signal_type": trade.signal_type,
                    "entry_price": trade.entry_price,
                    "exit_price": trade.exit_price,
                    "quantity": trade.quantity,
                    "pnl": trade.pnl,
                    "pnl_pct": trade.pnl_pct,
                    "status": trade.status,
                    "commission": trade.commission,
                    "slippage": trade.slippage,
                }
            )

        return pd.DataFrame(trade_data)

    def get_equity_curve_df(self) -> pd.DataFrame:
        """
        获取权益曲线DataFrame

        Returns:
            权益曲线DataFrame
        """
        return pd.DataFrame(self.equity_curve)


def main():
    """测试回测引擎"""
    # 这里应该导入信号生成器和数据
    # 由于模块依赖，这里只做基本测试
    print("回测引擎模块已创建")
    print("请在主程序中使用完整的数据和信号进行测试")


if __name__ == "__main__":
    main()
