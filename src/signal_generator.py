"""
交易信号生成模块

基于针指标和均线趋势判断生成买入/卖出信号
实现止损止盈逻辑
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple

try:
    from .needle_indicator import NeedleIndicator
    from .technical_indicators import TechnicalIndicators
except ImportError:
    from needle_indicator import NeedleIndicator
    from technical_indicators import TechnicalIndicators


class SignalGenerator:
    """交易信号生成器"""

    def __init__(
        self,
        needle_params: Dict = None,
        ma_period: int = 20,
        ma_type: str = "ema",
        min_needle_strength: float = 1.0,
        risk_reward_ratio: float = 2.0,
    ):
        """
        初始化信号生成器

        Args:
            needle_params: 针指标参数字典
            ma_period: 移动平均周期
            ma_type: 移动平均类型
            min_needle_strength: 最小针强度
            risk_reward_ratio: 风险收益比
        """
        self.needle_params = needle_params or {}
        self.ma_period = ma_period
        self.ma_type = ma_type
        self.min_needle_strength = min_needle_strength
        self.risk_reward_ratio = risk_reward_ratio

        # 初始化针指标计算器
        self.needle_indicator = NeedleIndicator(**self.needle_params)

    def calculate_trend_filter(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算趋势过滤器

        Args:
            df: 包含OHLC数据的DataFrame

        Returns:
            包含趋势信息的DataFrame
        """
        result = df.copy()

        # 计算移动平均线
        ma = TechnicalIndicators.calculate_moving_average(
            df["close"], self.ma_period, self.ma_type
        )
        result["ma"] = ma

        # 计算趋势方向
        result["trend"] = TechnicalIndicators.calculate_trend_direction(
            df, self.ma_period, self.ma_type
        )

        # 价格相对于均线的位置
        result["price_vs_ma"] = df["close"] - ma
        result["above_ma"] = result["price_vs_ma"] > 0
        result["below_ma"] = result["price_vs_ma"] < 0

        # 均线斜率
        result["ma_slope"] = ma.diff()
        result["ma_rising"] = result["ma_slope"] > 0
        result["ma_falling"] = result["ma_slope"] < 0

        return result

    def generate_entry_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成入场信号

        交易规则:
        1. 做多: 下针 + 上涨趋势 (价格在均线上方且均线上涨)
        2. 做空: 上针 + 下跌趋势 (价格在均线下方且均线下跌)
        3. 针强度必须大于最小阈值

        Args:
            df: 包含OHLC数据的DataFrame

        Returns:
            包含入场信号的DataFrame
        """
        # 计算针指标
        result = self.needle_indicator.get_needle_signals(df)

        # 计算趋势过滤器
        result = self.calculate_trend_filter(result)

        # 过滤强度足够的针
        strong_needles = result["needle_strength"] >= self.min_needle_strength

        # 做多信号条件
        long_conditions = (
            result["is_lower_needle"]  # 下针(看涨)
            & strong_needles  # 强度足够
            & result["above_ma"]  # 价格在均线上方
            & result["ma_rising"]  # 均线上涨
        )

        # 做空信号条件
        short_conditions = (
            result["is_upper_needle"]  # 上针(看跌)
            & strong_needles  # 强度足够
            & result["below_ma"]  # 价格在均线下方
            & result["ma_falling"]  # 均线下跌
        )

        result["long_signal"] = long_conditions
        result["short_signal"] = short_conditions
        result["entry_signal"] = long_conditions | short_conditions

        # 信号类型
        result["signal_type"] = np.where(
            long_conditions, "long", np.where(short_conditions, "short", "none")
        )

        return result

    def calculate_stop_loss_take_profit(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算止损和止盈价格

        止损规则:
        - 做多: 以下针的最低价作为止损
        - 做空: 以上针的最高价作为止损

        止盈规则:
        - 根据风险收益比计算止盈价格

        Args:
            df: 包含入场信号的DataFrame

        Returns:
            包含止损止盈的DataFrame
        """
        result = df.copy()

        # 入场价格 (下一根K线的开盘价)
        result["entry_price"] = result["open"].shift(-1)

        # 止损价格
        result["stop_loss"] = np.where(
            result["long_signal"],
            result["low"],  # 做多: 针的最低价
            np.where(
                result["short_signal"], result["high"], np.nan
            ),  # 做空: 针的最高价
        )

        # 计算风险 (入场价与止损价的差距)
        result["risk"] = np.where(
            result["long_signal"],
            result["entry_price"] - result["stop_loss"],  # 做多风险
            np.where(
                result["short_signal"],
                result["stop_loss"] - result["entry_price"],  # 做空风险
                np.nan,
            ),
        )

        # 计算止盈价格
        result["take_profit"] = np.where(
            result["long_signal"],
            result["entry_price"] + result["risk"] * self.risk_reward_ratio,  # 做多止盈
            np.where(
                result["short_signal"],
                result["entry_price"]
                - result["risk"] * self.risk_reward_ratio,  # 做空止盈
                np.nan,
            ),
        )

        return result

    def generate_complete_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成完整的交易信号

        Args:
            df: 包含OHLC数据的DataFrame

        Returns:
            包含完整交易信号的DataFrame
        """
        # 生成入场信号
        result = self.generate_entry_signals(df)

        # 计算止损止盈
        result = self.calculate_stop_loss_take_profit(result)

        # 过滤无效信号 (风险为0或负数的信号)
        valid_signals = (
            result["entry_signal"] & (result["risk"] > 0) & result["risk"].notna()
        )

        result["valid_signal"] = valid_signals

        return result

    def get_signal_summary(self, df: pd.DataFrame) -> Dict:
        """
        获取信号摘要统计

        Args:
            df: 包含交易信号的DataFrame

        Returns:
            信号统计字典
        """
        signals = df[df["valid_signal"]]

        summary = {
            "total_bars": len(df),
            "total_signals": len(signals),
            "signal_frequency": len(signals) / len(df) * 100 if len(df) > 0 else 0,
            "long_signals": (signals["signal_type"] == "long").sum(),
            "short_signals": (signals["signal_type"] == "short").sum(),
            "avg_risk": signals["risk"].mean() if len(signals) > 0 else 0,
            "avg_reward": (
                (signals["risk"] * self.risk_reward_ratio).mean()
                if len(signals) > 0
                else 0
            ),
            "avg_needle_strength": (
                signals["needle_strength"].mean() if len(signals) > 0 else 0
            ),
        }

        return summary

    def get_trade_list(self, df: pd.DataFrame) -> List[Dict]:
        """
        获取交易列表

        Args:
            df: 包含交易信号的DataFrame

        Returns:
            交易字典列表
        """
        signals = df[df["valid_signal"]].copy()

        trades = []
        for idx, row in signals.iterrows():
            trade = {
                "timestamp": idx,
                "signal_type": row["signal_type"],
                "entry_price": row["entry_price"],
                "stop_loss": row["stop_loss"],
                "take_profit": row["take_profit"],
                "risk": row["risk"],
                "reward": row["risk"] * self.risk_reward_ratio,
                "risk_reward_ratio": self.risk_reward_ratio,
                "needle_strength": row["needle_strength"],
                "needle_type": row["needle_type"],
                "trend": row["trend"],
                "ma_value": row["ma"],
            }
            trades.append(trade)

        return trades

    def filter_signals_by_time(
        self, df: pd.DataFrame, min_interval_hours: int = 4
    ) -> pd.DataFrame:
        """
        根据时间间隔过滤信号，避免过于频繁的交易

        Args:
            df: 包含交易信号的DataFrame
            min_interval_hours: 最小信号间隔(小时)

        Returns:
            过滤后的DataFrame
        """
        result = df.copy()
        signals = result[result["valid_signal"]].copy()

        if len(signals) == 0:
            return result

        # 按时间排序
        signals = signals.sort_index()

        # 过滤时间间隔
        filtered_indices = []
        last_signal_time = None

        for idx in signals.index:
            if last_signal_time is None:
                filtered_indices.append(idx)
                last_signal_time = idx
            else:
                time_diff = (idx - last_signal_time).total_seconds() / 3600
                if time_diff >= min_interval_hours:
                    filtered_indices.append(idx)
                    last_signal_time = idx

        # 更新有效信号标记
        result["valid_signal"] = False
        result.loc[filtered_indices, "valid_signal"] = True
        result.loc[filtered_indices, "entry_signal"] = True

        return result


def main():
    """测试信号生成功能"""
    # 创建测试数据
    dates = pd.date_range("2024-01-01", periods=500, freq="H")
    np.random.seed(42)

    # 模拟趋势性价格数据
    price = 50000
    trend = 1  # 1: 上涨, -1: 下跌
    prices = []

    for i in range(500):
        # 每100根K线改变一次趋势
        if i % 100 == 0:
            trend *= -1

        # 基础趋势变化
        trend_change = trend * np.random.uniform(0.001, 0.003)
        noise = np.random.normal(0, 0.005)

        open_price = price
        close_price = price * (1 + trend_change + noise)

        # 偶尔创建针形态
        if i % 30 == 0 and trend == 1:  # 上涨趋势中的下针
            high_price = max(open_price, close_price) * (
                1 + np.random.uniform(0, 0.002)
            )
            low_price = min(open_price, close_price) * (
                1 - np.random.uniform(0.01, 0.02)
            )
        elif i % 35 == 0 and trend == -1:  # 下跌趋势中的上针
            high_price = max(open_price, close_price) * (
                1 + np.random.uniform(0.01, 0.02)
            )
            low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.002))
        else:  # 正常K线
            high_price = max(open_price, close_price) * (
                1 + np.random.uniform(0, 0.005)
            )
            low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.005))

        prices.append(
            {
                "open": open_price,
                "high": high_price,
                "low": low_price,
                "close": close_price,
            }
        )
        price = close_price

    # 创建DataFrame
    df = pd.DataFrame(prices, index=dates)
    df["volume"] = np.random.uniform(100, 1000, len(df))

    print("测试数据创建完成")
    print(f"数据形状: {df.shape}")
    print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")

    # 初始化信号生成器
    signal_generator = SignalGenerator(
        ma_period=20, ma_type="ema", min_needle_strength=1.0, risk_reward_ratio=2.0
    )

    # 生成交易信号
    print("\n生成交易信号...")
    signal_df = signal_generator.generate_complete_signals(df)

    # 过滤信号
    filtered_df = signal_generator.filter_signals_by_time(
        signal_df, min_interval_hours=4
    )

    # 获取信号摘要
    summary = signal_generator.get_signal_summary(filtered_df)
    print(f"\n信号摘要:")
    for key, value in summary.items():
        print(f"{key}: {value}")

    # 获取交易列表
    trades = signal_generator.get_trade_list(filtered_df)
    print(f"\n交易列表 (前5个):")
    for i, trade in enumerate(trades[:5]):
        print(f"交易 {i+1}:")
        for key, value in trade.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
            else:
                print(f"  {key}: {value}")
        print()


if __name__ == "__main__":
    main()
