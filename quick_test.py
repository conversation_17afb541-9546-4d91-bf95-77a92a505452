"""
快速测试脚本 - 测试核心功能
"""
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# 添加src目录到路径
sys.path.append('src')

def create_test_data():
    """创建测试数据"""
    dates = pd.date_range('2024-01-01', periods=100, freq='h')
    np.random.seed(42)
    
    # 创建包含针形态的价格数据
    data = []
    price = 50000
    
    for i in range(100):
        if i % 20 == 0:  # 每20根K线创建一个明显的针
            open_price = price
            close_price = price + np.random.uniform(-100, 100)
            # 创建长影线
            if i % 40 == 0:  # 上针
                high_price = max(open_price, close_price) + np.random.uniform(800, 1200)
                low_price = min(open_price, close_price) - np.random.uniform(0, 100)
            else:  # 下针
                high_price = max(open_price, close_price) + np.random.uniform(0, 100)
                low_price = min(open_price, close_price) - np.random.uniform(800, 1200)
        else:
            # 正常K线
            change = np.random.normal(0, 0.01)
            open_price = price
            close_price = price * (1 + change)
            high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.005))
            low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.005))
        
        data.append({
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': np.random.uniform(100, 1000)
        })
        price = close_price
    
    return pd.DataFrame(data, index=dates)

def test_complete_workflow():
    """测试完整工作流程"""
    print("=" * 60)
    print("针策略回测系统 - 快速测试")
    print("=" * 60)
    
    # 1. 创建测试数据
    print("1. 创建测试数据...")
    df = create_test_data()
    print(f"   数据形状: {df.shape}")
    print(f"   价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    # 2. 测试针指标
    print("\n2. 测试针指标...")
    from needle_indicator import NeedleIndicator
    
    needle_indicator = NeedleIndicator()
    needle_df = needle_indicator.get_needle_signals(df)
    
    needle_count = needle_df['is_needle'].sum()
    upper_needles = needle_df['is_upper_needle'].sum()
    lower_needles = needle_df['is_lower_needle'].sum()
    
    print(f"   发现针数量: {needle_count}")
    print(f"   上针: {upper_needles}, 下针: {lower_needles}")
    
    if needle_count > 0:
        avg_strength = needle_df[needle_df['is_needle']]['needle_strength'].mean()
        print(f"   平均针强度: {avg_strength:.2f}")
    
    # 3. 测试信号生成
    print("\n3. 测试信号生成...")
    from signal_generator import SignalGenerator
    
    signal_generator = SignalGenerator(
        min_needle_strength=0.5,  # 降低阈值以便测试
        ma_period=10
    )
    
    signals_df = signal_generator.generate_complete_signals(df)
    signal_count = signals_df['valid_signal'].sum()
    
    print(f"   生成信号数量: {signal_count}")
    
    if signal_count > 0:
        long_signals = (signals_df['signal_type'] == 'long').sum()
        short_signals = (signals_df['signal_type'] == 'short').sum()
        print(f"   做多信号: {long_signals}, 做空信号: {short_signals}")
    
    # 4. 测试回测引擎
    print("\n4. 测试回测引擎...")
    from backtest_engine import BacktestEngine
    
    backtest_engine = BacktestEngine(initial_capital=100000)
    metrics = backtest_engine.run_backtest(df, signals_df)
    
    print(f"   总交易数: {metrics['total_trades']}")
    print(f"   总收益率: {metrics['total_return']:.2f}%")
    print(f"   胜率: {metrics['win_rate']:.2f}%")
    
    # 5. 创建输出目录并保存结果
    print("\n5. 保存测试结果...")
    output_dir = "test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存数据
    df.to_csv(f"{output_dir}/test_data.csv")
    signals_df.to_csv(f"{output_dir}/test_signals.csv")
    
    # 保存指标
    import json
    with open(f"{output_dir}/test_metrics.json", 'w') as f:
        json.dump(metrics, f, indent=2)
    
    print(f"   结果已保存到: {output_dir}/")
    
    print("\n" + "=" * 60)
    print("快速测试完成！")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        test_complete_workflow()
        print("✓ 所有测试通过！系统运行正常。")
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
