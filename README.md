# 数字货币"高频接针"策略回测系统

这是一个完整的数字货币交易策略回测系统，专门用于"接针"策略的回测和参数优化。

## 功能特性

- **针指标设计**: 基于影线长度相对ATR比值、实体占比、反向影线占比的综合针指标
- **趋势过滤**: 使用移动平均线判断趋势方向，避免逆市交易
- **风险管理**: 以针的极值点作为止损，根据风险收益比计算止盈
- **完整回测**: 包含资金管理、手续费、滑点等真实交易成本
- **参数优化**: 支持网格搜索和随机搜索优化
- **统计分析**: 分析针指标分布，为参数优化提供合理范围
- **可视化报告**: 生成详细的图表和回测报告

## 系统架构

```
needle_strategy/
├── src/                          # 源代码目录
│   ├── data_downloader.py        # 数据下载模块
│   ├── technical_indicators.py   # 技术指标计算
│   ├── needle_indicator.py       # 针指标定义与实现
│   ├── signal_generator.py       # 交易信号生成
│   ├── backtest_engine.py        # 回测引擎
│   ├── parameter_optimizer.py    # 参数优化框架
│   ├── statistical_analysis.py   # 统计分析模块
│   └── visualization.py          # 可视化与报告
├── main.py                       # 主程序
├── test_modules.py               # 模块测试脚本
├── config.json                   # 配置文件
├── requirements.txt              # 依赖包列表
└── README.md                     # 说明文档
```

## 安装与配置

### 1. 环境要求

- Python 3.9+
- 虚拟环境 (推荐)

### 2. 安装依赖

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 测试系统

```bash
python test_modules.py
```

## 使用方法

### 1. 基本回测

```bash
# 使用默认参数运行回测
python main.py

# 指定交易对和时间范围
python main.py --symbol ETHUSDT --start_date 2024-01-01 --end_date 2024-06-30

# 使用配置文件
python main.py --config config.json
```

### 2. 参数优化

```bash
# 网格搜索优化
python main.py --optimize --method grid_search

# 随机搜索优化
python main.py --optimize --method random_search
```

### 3. 配置文件

编辑 `config.json` 文件来自定义参数：

```json
{
  "symbol": "BTCUSDT",
  "interval": "1h",
  "start_date": "2024-01-01",
  "end_date": "2024-06-30",
  "initial_capital": 100000,
  "commission_rate": 0.001,
  "slippage_rate": 0.0005,
  "risk_per_trade": 0.02,
  "needle_params": {
    "atr_period": 14,
    "min_shadow_atr_ratio": 1.5,
    "max_body_ratio": 0.3,
    "max_opposite_shadow_ratio": 0.2
  },
  "signal_params": {
    "ma_period": 20,
    "ma_type": "ema",
    "min_needle_strength": 1.0,
    "risk_reward_ratio": 2.0
  }
}
```

## 策略说明

### 针指标定义

针形态需要同时满足以下条件：

1. **影线长度**: 上影线或下影线长度 ≥ 1.5 × ATR
2. **实体占比**: 实体长度占整个K线的比例 ≤ 30%
3. **反向影线**: 相反方向影线占比 ≤ 20%

### 交易规则

1. **做多条件**: 
   - 出现下针（看涨针）
   - 价格在均线上方
   - 均线呈上涨趋势
   - 针强度 ≥ 最小阈值

2. **做空条件**:
   - 出现上针（看跌针）
   - 价格在均线下方
   - 均线呈下跌趋势
   - 针强度 ≥ 最小阈值

3. **风险管理**:
   - 止损：针的极值点（上针的最高价/下针的最低价）
   - 止盈：根据风险收益比计算
   - 仓位：基于固定风险比例计算

## 输出结果

系统会在 `output/` 目录下创建以参数命名的子目录，包含：

- `price_data.csv`: 原始价格数据
- `signals.csv`: 交易信号数据
- `trades.csv`: 交易记录
- `equity_curve.csv`: 权益曲线数据
- `metrics.json`: 绩效指标
- `equity_curve.png`: 权益曲线图
- `trade_analysis.png`: 交易分析图
- `signal_distribution.png`: 信号分布图
- `interactive_chart.html`: 交互式图表
- `performance_report.txt`: 详细绩效报告
- `needle_analysis.json`: 针指标分析结果
- `analysis_report.txt`: 统计分析报告

## 主要指标

- **总收益率**: 策略总收益百分比
- **胜率**: 盈利交易占总交易的比例
- **最大回撤**: 权益曲线的最大回撤百分比
- **夏普比率**: 风险调整后收益指标
- **盈亏比**: 平均盈利与平均亏损的比值
- **交易频率**: 平均交易频次

## 注意事项

1. **数据来源**: 系统从 Binance Vision 下载历史数据，需要网络连接
2. **计算资源**: 参数优化可能需要较长时间，建议使用多核CPU
3. **回测偏差**: 回测结果仅供参考，实际交易可能存在差异
4. **风险提示**: 数字货币交易存在高风险，请谨慎投资

## 扩展功能

系统采用模块化设计，可以轻松扩展：

- 添加新的技术指标
- 实现其他优化算法
- 集成实时交易接口
- 添加更多可视化图表
- 支持多品种组合策略

## 技术支持

如有问题或建议，请查看代码注释或联系开发者。

## 免责声明

本系统仅用于学习和研究目的，不构成投资建议。使用者应当充分了解数字货币交易的风险，并根据自身情况谨慎决策。开发者不对使用本系统造成的任何损失承担责任。
